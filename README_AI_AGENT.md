# AI Stock Analysis Agent

A comprehensive AI-powered stock analysis system that provides fundamental, technical, and risk analysis with investment recommendations using local LLM models via Ollama.

## 🚀 Features

- **Multi-Agent Analysis**: Fundamental, Technical, Risk, and Portfolio Management agents
- **Local LLM Integration**: Uses Ollama for private, local AI inference
- **Real Data Integration**: Leverages your existing ScreenerScraper and StrikeAPI
- **Comprehensive Reports**: Detailed analysis with confidence scores and reasoning
- **CLI Interface**: Easy-to-use command line interface with interactive mode
- **Batch Processing**: Analyze multiple stocks simultaneously
- **Export Capabilities**: JSON export for further analysis

## 🏗️ Architecture

```
AI Stock Analysis Agent
├── Data Layer
│   ├── ScreenerScraper (Fundamental Data)
│   ├── StrikeAPI (Price Data)
│   └── Data Adapters
├── Analysis Agents
│   ├── Fundamental Analysis Agent
│   ├── Technical Analysis Agent
│   ├── Risk Management Agent
│   └── Portfolio Management Agent
├── LLM Integration
│   ├── Ollama Models
│   ├── Prompt Templates
│   └── Response Processing
├── Workflow Orchestration
│   ├── LangGraph Workflow
│   ├── State Management
│   └── Error Handling
└── User Interface
    ├── CLI Interface
    ├── Interactive Mode
    └── Result Display
```

## 📋 Prerequisites

1. **Python 3.8+** with required packages
2. **Ollama** installed and running
3. **LLM Model** (e.g., llama3.2:latest)
4. **Your existing APIs** (ScreenerScraper and StrikeAPI)

## 🛠️ Installation

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Install and Setup Ollama**:
   ```bash
   # Install Ollama (visit https://ollama.ai for platform-specific instructions)
   
   # Pull a model
   ollama pull llama3.2:latest
   
   # Start Ollama (if not running as service)
   ollama serve
   ```

3. **Configure Environment** (optional):
   ```bash
   cp .env.example .env
   # Edit .env with your preferences
   ```

## 🚀 Quick Start

### Single Stock Analysis
```bash
# Analyze a single stock
python -m app.ai_agent.cli TATAMOTORS

# With custom parameters
python -m app.ai_agent.cli TATAMOTORS --risk-tolerance high --timeframe long_term --export
```

### Multiple Stocks Analysis
```bash
# Analyze multiple stocks
python -m app.ai_agent.cli TATAMOTORS RELIANCE INFY

# Batch analysis with export
python -m app.ai_agent.cli TATAMOTORS RELIANCE INFY --export
```

### Interactive Mode
```bash
# Start interactive mode
python -m app.ai_agent.cli --interactive
```

### Test the System
```bash
# Run system tests
python app/ai_agent/test_system.py
```

## 📊 Analysis Components

### 1. Fundamental Analysis
- **Profitability Metrics**: ROE, margins, efficiency ratios
- **Growth Analysis**: Revenue, earnings, book value growth
- **Financial Health**: Debt ratios, liquidity, cash flow
- **Valuation Metrics**: P/E, P/B, P/S ratios

### 2. Technical Analysis
- **Trend Analysis**: Moving averages, trend strength
- **Momentum Indicators**: RSI, MACD, momentum signals
- **Support/Resistance**: Key price levels
- **Volume Analysis**: Volume patterns and confirmation

### 3. Risk Assessment
- **Market Risk**: Volatility, market conditions
- **Company Risk**: Financial stability, sector risks
- **Position Sizing**: Recommended allocation
- **Risk Mitigation**: Stop-loss and monitoring points

### 4. Portfolio Recommendation
- **Final Decision**: Buy/Sell/Hold recommendation
- **Confidence Score**: AI confidence in the recommendation
- **Price Targets**: Target price and stop-loss levels
- **Investment Rationale**: Detailed reasoning

## 🎯 Key Benefits

### Compared to Traditional Analysis
- **AI-Powered Insights**: Leverages advanced LLM reasoning
- **Comprehensive Coverage**: Multiple analysis dimensions
- **Consistent Methodology**: Eliminates human bias
- **Scalable**: Analyze multiple stocks efficiently
- **Local Processing**: Private, secure analysis

### Integration with Your Existing System
- **Seamless Data Flow**: Uses your ScreenerScraper and StrikeAPI
- **No External Dependencies**: All analysis runs locally
- **Extensible Architecture**: Easy to add new analysis agents
- **Configurable**: Adapt to your investment style

## 🎯 Usage Examples

### Example 1: Basic Analysis
```bash
python -m app.ai_agent.cli TATAMOTORS
```

**Output**:
```
============================================================
                AI STOCK ANALYSIS - TATAMOTORS
============================================================

Company Information
-------------------
Company: Tata Motors Limited
Symbol: TATAMOTORS
Sector: Auto
Current Price: ₹485.30
Market Cap: ₹1,58,234.50 Cr

Fundamental Analysis
--------------------
📈 BULLISH (Confidence: 0.75)

Key Insights:
  • Profitability Analysis: Strong ROE above industry average
  • Growth Analysis: Consistent revenue growth in EV segment
  • Valuation Analysis: Trading below intrinsic value

Technical Analysis
------------------
➡️ NEUTRAL (Confidence: 0.60)

Key Insights:
  • Trend Analysis: Sideways movement with support at ₹470
  • Momentum Analysis: RSI neutral at 52
  • Volume Analysis: Average volume activity

Risk Analysis
-------------
⚠️ MEDIUM (Confidence: 0.70)

Final Investment Recommendation
===============================
📈 BUY (Confidence: 0.72)

Risk Level: MEDIUM
Analysis Quality: 85.0%

Key Decision Factors:
  • Strong fundamental metrics outweigh technical neutrality
  • EV transition providing growth opportunities
  • Target Price: ₹520.00
  • Stop Loss: ₹450.00
```

### Example 2: Batch Analysis
```bash
python -m app.ai_agent.cli TATAMOTORS RELIANCE INFY --export
```

### Example 3: Interactive Mode
```bash
python -m app.ai_agent.cli --interactive

Options:
1. Analyze single stock
2. Analyze multiple stocks
3. Exit

Enter your choice (1-3): 1
Enter stock symbol: RELIANCE
Risk tolerance (low/medium/high) [medium]: high
Investment timeframe (short_term/medium_term/long_term) [medium_term]: long_term
Export results to JSON? (y/n) [n]: y
```

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# Ollama Configuration
OLLAMA_HOST=localhost
OLLAMA_BASE_URL=http://localhost:11434

# Default model for analysis
DEFAULT_MODEL=llama3.2:latest

# Analysis Configuration
ANALYSIS_CONFIDENCE_THRESHOLD=0.6
RISK_TOLERANCE=medium
```

### Risk Tolerance Levels
- **Low**: Conservative analysis, higher confidence thresholds
- **Medium**: Balanced approach (default)
- **High**: Aggressive analysis, accepts higher risk

### Investment Timeframes
- **Short Term**: 1-6 months, focuses on technical signals
- **Medium Term**: 6-18 months, balanced approach (default)
- **Long Term**: 18+ months, emphasizes fundamentals

## 🔧 Advanced Usage

### Custom Model Configuration
```python
from app.ai_agent.llm.models import get_ollama_model

# Use a specific model
model = get_ollama_model("mistral:7b")
```

### Programmatic Usage
```python
from app.ai_agent.workflow.graph import run_stock_analysis

# Run analysis programmatically
result = run_stock_analysis("TATAMOTORS", "medium", "long_term")
print(result.final_recommendation)
```

### Batch Processing Script
```python
from app.ai_agent.workflow.graph import run_batch_analysis

symbols = ["TATAMOTORS", "RELIANCE", "INFY", "TCS", "HDFCBANK"]
results = run_batch_analysis(symbols, "medium", "medium_term")

for symbol, result in results.items():
    if result.final_recommendation:
        action = result.final_recommendation["recommendation"]["action"]
        confidence = result.final_recommendation["recommendation"]["confidence"]
        print(f"{symbol}: {action} (confidence: {confidence:.2f})")
```

## 🐛 Troubleshooting

### Common Issues

1. **Ollama Connection Failed**
   ```bash
   # Check if Ollama is running
   curl http://localhost:11434/api/tags
   
   # Start Ollama if not running
   ollama serve
   ```

2. **Model Not Found**
   ```bash
   # Pull the required model
   ollama pull llama3.2:latest
   
   # List available models
   ollama list
   ```

3. **Data Collection Errors**
   - Ensure ScreenerScraper cache is accessible
   - Check StrikeAPI connectivity
   - Verify symbol format (use NSE symbols)

4. **Analysis Failures**
   - Check LLM model availability
   - Verify sufficient historical data
   - Review error messages in output

### Debug Mode
```bash
# Run with detailed error information
python -m app.ai_agent.cli TATAMOTORS --skip-checks
```

## 📈 Performance Tips

1. **Model Selection**: Larger models (7B+) provide better analysis quality
2. **Data Quality**: Ensure sufficient historical data (1+ year recommended)
3. **Batch Processing**: Use batch mode for multiple stocks to optimize performance
4. **Caching**: ScreenerScraper caching reduces API calls and improves speed

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

This project is part of the Jarvis stock analysis system.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Run the test system: `python app/ai_agent/test_system.py`
3. Review error logs and messages
4. Ensure all prerequisites are met

---

**Happy Analyzing! 📊🚀**
