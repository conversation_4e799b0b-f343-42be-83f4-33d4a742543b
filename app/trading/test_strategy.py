"""
Test script for Retail Trap Momentum Strategy

This script tests the strategy implementation to ensure everything works correctly.
"""

import sys
import os
from datetime import datetime

# Add parent directories to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app.trading.strategy_runner import StrategyRunner
from app.trading.strategies.retail_trap_momentum import RetailTrapMomentumStrategy


def test_strategy_initialization():
    """Test strategy initialization"""
    print("🧪 Testing strategy initialization...")
    
    try:
        # Test 5m timeframe
        strategy_5m = RetailTrapMomentumStrategy(timeframe="5m")
        assert strategy_5m.timeframe == "5m"
        assert strategy_5m.name == "Retail Trap Momentum"
        print("   ✅ 5m strategy initialized successfully")
        
        # Test 3m timeframe
        strategy_3m = RetailTrapMomentumStrategy(timeframe="3m")
        assert strategy_3m.timeframe == "3m"
        print("   ✅ 3m strategy initialized successfully")
        
        # Test strategy runner
        runner = StrategyRunner(timeframe="5m")
        assert runner.timeframe == "5m"
        print("   ✅ Strategy runner initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Initialization failed: {e}")
        return False


def test_data_fetching():
    """Test data fetching from StrikeAPI"""
    print("\n🧪 Testing data fetching...")
    
    try:
        strategy = RetailTrapMomentumStrategy(timeframe="5m")
        
        # Test with a known symbol
        test_symbol = "TATAMOTORS"
        print(f"   📊 Fetching data for {test_symbol}...")
        
        # Get price data
        price_data = strategy.strike_api.get_price_data_simple(test_symbol, days_back=5)
        
        if price_data and len(price_data) > 0:
            print(f"   ✅ Fetched {len(price_data)} price records")
            
            # Test dataframe creation
            df = strategy._create_dataframe(price_data)
            if df is not None and len(df) > 0:
                print(f"   ✅ Created dataframe with {len(df)} rows")
                
                # Test indicator calculation
                df_with_indicators = strategy._calculate_indicators(df)
                if 'rsi' in df_with_indicators.columns:
                    print("   ✅ Technical indicators calculated successfully")
                    return True
                else:
                    print("   ❌ Technical indicators not calculated")
                    return False
            else:
                print("   ❌ Failed to create dataframe")
                return False
        else:
            print("   ❌ No price data fetched")
            return False
            
    except Exception as e:
        print(f"   ❌ Data fetching failed: {e}")
        return False


def test_pattern_detection():
    """Test pattern detection"""
    print("\n🧪 Testing pattern detection...")
    
    try:
        runner = StrategyRunner(timeframe="5m")
        test_symbol = "TATAMOTORS"
        
        print(f"   🔍 Analyzing {test_symbol} for patterns...")
        
        # Get strategy state
        state = runner.get_strategy_state(test_symbol, days_back=7)
        
        if state:
            print(f"   ✅ Strategy state created successfully")
            print(f"   📊 Market condition: {state.market_condition.value}")
            print(f"   📈 RSI: {state.rsi:.1f}")
            print(f"   📊 Volume ratio: {state.volume_profile.volume_ratio:.1f}x")
            print(f"   🎯 Patterns detected: {len(state.detected_patterns)}")
            
            if state.detected_patterns:
                for pattern in state.detected_patterns:
                    confidence = state.pattern_confidence.get(pattern.value, 0)
                    print(f"      • {pattern.value}: {confidence:.1%}")
            
            print(f"   🎯 Support levels: {len(state.support_levels)}")
            print(f"   🎯 Resistance levels: {len(state.resistance_levels)}")
            
            return True
        else:
            print("   ❌ Failed to create strategy state")
            return False
            
    except Exception as e:
        print(f"   ❌ Pattern detection failed: {e}")
        return False


def test_signal_generation():
    """Test signal generation"""
    print("\n🧪 Testing signal generation...")
    
    try:
        runner = StrategyRunner(timeframe="5m")
        
        # Test with multiple symbols to increase chance of finding a signal
        test_symbols = ["TATAMOTORS", "RELIANCE", "TCS", "INFY", "HDFCBANK"]
        
        signals_found = 0
        
        for symbol in test_symbols:
            print(f"   🔍 Testing signal generation for {symbol}...")
            
            try:
                signal = runner.get_signal(symbol, days_back=7)
                
                if signal:
                    signals_found += 1
                    print(f"   ✅ Signal generated for {symbol}:")
                    print(f"      Type: {signal.signal_type.value}")
                    print(f"      Confidence: {signal.confidence:.1%}")
                    print(f"      Entry: ₹{signal.entry_price:.2f}")
                    print(f"      Stop Loss: ₹{signal.stop_loss:.2f}")
                    print(f"      Take Profit: ₹{signal.take_profit:.2f}")
                    print(f"      Risk:Reward: 1:{signal.risk_reward_ratio:.1f}")
                else:
                    print(f"   ⚪ No signal for {symbol}")
                    
            except Exception as e:
                print(f"   ⚠️  Error testing {symbol}: {e}")
        
        print(f"\n   📊 Signal generation test complete:")
        print(f"   🎯 Signals found: {signals_found}/{len(test_symbols)}")
        
        if signals_found > 0:
            print("   ✅ Signal generation working correctly")
            return True
        else:
            print("   ⚠️  No signals found (this is normal if market conditions don't match patterns)")
            return True  # This is actually OK - no signals can be normal
            
    except Exception as e:
        print(f"   ❌ Signal generation test failed: {e}")
        return False


def test_cli_interface():
    """Test CLI interface"""
    print("\n🧪 Testing CLI interface...")
    
    try:
        # Import CLI functions
        from app.trading.cli import quick_signal
        
        print("   🖥️  Testing quick_signal function...")
        
        # This should not crash
        quick_signal("TATAMOTORS", "5m")
        
        print("   ✅ CLI interface working")
        return True
        
    except Exception as e:
        print(f"   ❌ CLI test failed: {e}")
        return False


def run_all_tests():
    """Run all tests"""
    print("🚀 RETAIL TRAP MOMENTUM STRATEGY - TEST SUITE")
    print("=" * 60)
    print(f"📅 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Strategy Initialization", test_strategy_initialization),
        ("Data Fetching", test_data_fetching),
        ("Pattern Detection", test_pattern_detection),
        ("Signal Generation", test_signal_generation),
        ("CLI Interface", test_cli_interface)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   💥 {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Strategy is ready for use.")
    elif passed >= total * 0.8:
        print("⚠️  Most tests passed. Strategy should work but check failed tests.")
    else:
        print("❌ Multiple test failures. Please check the implementation.")
    
    print(f"\n💡 To use the strategy:")
    print(f"   python -m app.trading.cli TATAMOTORS")
    print(f"   python -m app.trading.cli --scan")
    print(f"   python app/trading/examples/retail_trap_example.py")


if __name__ == "__main__":
    run_all_tests()
