"""
Simple Backtesting Framework for Retail Trap Momentum Strategy

This provides basic backtesting capabilities to validate strategy performance
on historical data.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import sys
import os

# Add parent directories to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.trading.strategies.retail_trap_momentum import RetailTrapMomentumStrategy
from app.trading.models import TradingSignal, SignalType
from app.data.strike_api import StrikeAPI


class BacktestResult:
    """Results of a backtest"""
    
    def __init__(self):
        self.trades = []
        self.total_return = 0.0
        self.win_rate = 0.0
        self.avg_win = 0.0
        self.avg_loss = 0.0
        self.max_drawdown = 0.0
        self.sharpe_ratio = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        self.start_date = None
        self.end_date = None
        self.initial_capital = 100000  # ₹1 lakh
        self.final_capital = 100000


class Trade:
    """Individual trade record"""
    
    def __init__(self, signal: TradingSignal):
        self.symbol = signal.symbol
        self.signal_type = signal.signal_type
        self.entry_price = signal.entry_price
        self.stop_loss = signal.stop_loss
        self.take_profit = signal.take_profit
        self.entry_time = signal.timestamp
        self.exit_time = None
        self.exit_price = None
        self.pnl = 0.0
        self.pnl_percent = 0.0
        self.status = "OPEN"  # OPEN, WIN, LOSS, STOPPED
        self.confidence = signal.confidence
        self.pattern = signal.reasoning.get('pattern', 'Unknown')


class SimpleBacktester:
    """Simple backtesting engine"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.strategy = RetailTrapMomentumStrategy(timeframe="5m")
        self.strike_api = StrikeAPI()
    
    def backtest_symbol(self, symbol: str, days_back: int = 30) -> BacktestResult:
        """
        Backtest strategy on a single symbol
        
        Args:
            symbol: Stock symbol to test
            days_back: Number of days of historical data
            
        Returns:
            BacktestResult with performance metrics
        """
        print(f"🔄 Backtesting {symbol} over {days_back} days...")
        
        try:
            # Get historical data
            price_data = self.strike_api.get_price_data_simple(symbol, days_back)
            if not price_data or len(price_data) < 50:
                print(f"❌ Insufficient data for {symbol}")
                return BacktestResult()
            
            # Convert to DataFrame
            df = self._create_dataframe(price_data)
            if df is None:
                return BacktestResult()
            
            # Simulate trading
            result = self._simulate_trading(symbol, df)
            
            print(f"✅ Backtest complete for {symbol}")
            return result
            
        except Exception as e:
            print(f"❌ Backtest failed for {symbol}: {e}")
            return BacktestResult()
    
    def backtest_multiple_symbols(self, symbols: List[str], days_back: int = 30) -> Dict[str, BacktestResult]:
        """Backtest multiple symbols"""
        results = {}
        
        print(f"🔄 Backtesting {len(symbols)} symbols...")
        
        for symbol in symbols:
            results[symbol] = self.backtest_symbol(symbol, days_back)
        
        return results
    
    def _create_dataframe(self, price_data) -> Optional[pd.DataFrame]:
        """Convert price data to DataFrame"""
        try:
            data = []
            for tick in price_data:
                data.append({
                    'datetime': pd.to_datetime(tick.datetime),
                    'open': tick.open,
                    'high': tick.high,
                    'low': tick.low,
                    'close': tick.close,
                    'volume': tick.volume
                })
            
            df = pd.DataFrame(data)
            df.set_index('datetime', inplace=True)
            df.sort_index(inplace=True)
            return df
            
        except Exception as e:
            print(f"Error creating dataframe: {e}")
            return None
    
    def _simulate_trading(self, symbol: str, df: pd.DataFrame) -> BacktestResult:
        """Simulate trading on historical data"""
        result = BacktestResult()
        result.start_date = df.index[0]
        result.end_date = df.index[-1]
        result.initial_capital = self.initial_capital
        
        trades = []
        capital = self.initial_capital
        
        # Walk through data day by day
        lookback_window = 50  # Need enough data for indicators
        
        for i in range(lookback_window, len(df)):
            current_date = df.index[i]
            
            # Get data up to current point
            historical_data = df.iloc[:i+1]
            
            try:
                # Check for signal (simulate real-time analysis)
                signal = self._get_signal_at_point(symbol, historical_data)
                
                if signal:
                    # Create trade
                    trade = Trade(signal)
                    
                    # Simulate trade execution over next few periods
                    exit_result = self._simulate_trade_exit(trade, df.iloc[i:i+20])  # Look ahead 20 periods
                    
                    if exit_result:
                        trade.exit_time = exit_result['exit_time']
                        trade.exit_price = exit_result['exit_price']
                        trade.status = exit_result['status']
                        
                        # Calculate P&L
                        if trade.signal_type == SignalType.BUY:
                            trade.pnl_percent = (trade.exit_price - trade.entry_price) / trade.entry_price
                        else:  # SELL
                            trade.pnl_percent = (trade.entry_price - trade.exit_price) / trade.entry_price
                        
                        # Position sizing (2% risk)
                        risk_amount = capital * 0.02
                        position_size = risk_amount / abs(trade.entry_price - trade.stop_loss)
                        trade.pnl = trade.pnl_percent * position_size * trade.entry_price
                        
                        capital += trade.pnl
                        trades.append(trade)
                        
                        print(f"   📊 Trade: {trade.status} {trade.pnl_percent:+.2%} on {current_date.strftime('%Y-%m-%d')}")
            
            except Exception as e:
                # Skip this point if analysis fails
                continue
        
        # Calculate results
        result.trades = trades
        result.final_capital = capital
        result.total_return = (capital - self.initial_capital) / self.initial_capital
        result.total_trades = len(trades)
        
        if trades:
            winning_trades = [t for t in trades if t.pnl > 0]
            losing_trades = [t for t in trades if t.pnl <= 0]
            
            result.winning_trades = len(winning_trades)
            result.losing_trades = len(losing_trades)
            result.win_rate = len(winning_trades) / len(trades)
            
            if winning_trades:
                result.avg_win = np.mean([t.pnl_percent for t in winning_trades])
            if losing_trades:
                result.avg_loss = np.mean([t.pnl_percent for t in losing_trades])
        
        return result
    
    def _get_signal_at_point(self, symbol: str, historical_data: pd.DataFrame) -> Optional[TradingSignal]:
        """Get signal at a specific point in time (simulating real-time)"""
        try:
            # Convert DataFrame back to price tick format for strategy
            price_data = []
            for idx, row in historical_data.iterrows():
                # Create a mock price tick
                class MockTick:
                    def __init__(self, datetime, open, high, low, close, volume):
                        self.datetime = datetime.isoformat()
                        self.open = open
                        self.high = high
                        self.low = low
                        self.close = close
                        self.volume = volume
                
                price_data.append(MockTick(idx, row['open'], row['high'], row['low'], row['close'], row['volume']))
            
            # Use strategy to analyze
            df = self.strategy._create_dataframe(price_data)
            if df is None or len(df) < 50:
                return None
            
            df = self.strategy._calculate_indicators(df)
            
            # Get strategy state
            support_levels, resistance_levels = self.strategy._detect_support_resistance(df)
            volume_profile = self.strategy._analyze_volume(df)
            price_action = self.strategy._analyze_price_action(df)
            patterns, pattern_confidence = self.strategy._detect_retail_patterns(df, support_levels, resistance_levels)
            
            # Check for signals using signal generator
            current_price = df['close'].iloc[-1]
            
            # Create mock state
            class MockState:
                def __init__(self):
                    self.symbol = symbol
                    self.timeframe = "5m"
                    self.detected_patterns = patterns
                    self.pattern_confidence = pattern_confidence
                    self.support_levels = support_levels
                    self.resistance_levels = resistance_levels
                    self.rsi = df['rsi'].iloc[-1]
                    self.rsi_trend = "sideways"
                    self.volume_profile = volume_profile
                    self.price_action = price_action
            
            state = MockState()
            
            # Check for signals
            signal = self.strategy.signal_generator.check_false_breakout_signal(df, state, current_price)
            if signal:
                return signal
            
            signal = self.strategy.signal_generator.check_volume_spike_reversal(df, state, current_price)
            if signal:
                return signal
            
            return None
            
        except Exception as e:
            return None
    
    def _simulate_trade_exit(self, trade: Trade, future_data: pd.DataFrame) -> Optional[Dict[str, Any]]:
        """Simulate how a trade would exit"""
        if len(future_data) == 0:
            return None
        
        for idx, row in future_data.iterrows():
            # Check stop loss
            if trade.signal_type == SignalType.BUY:
                if row['low'] <= trade.stop_loss:
                    return {
                        'exit_time': idx,
                        'exit_price': trade.stop_loss,
                        'status': 'LOSS'
                    }
                elif row['high'] >= trade.take_profit:
                    return {
                        'exit_time': idx,
                        'exit_price': trade.take_profit,
                        'status': 'WIN'
                    }
            else:  # SELL
                if row['high'] >= trade.stop_loss:
                    return {
                        'exit_time': idx,
                        'exit_price': trade.stop_loss,
                        'status': 'LOSS'
                    }
                elif row['low'] <= trade.take_profit:
                    return {
                        'exit_time': idx,
                        'exit_price': trade.take_profit,
                        'status': 'WIN'
                    }
        
        # If no exit triggered, exit at last price
        return {
            'exit_time': future_data.index[-1],
            'exit_price': future_data['close'].iloc[-1],
            'status': 'TIMEOUT'
        }
    
    def print_results(self, symbol: str, result: BacktestResult):
        """Print backtest results"""
        print(f"""
📊 BACKTEST RESULTS - {symbol}
{'='*50}
📅 Period: {result.start_date.strftime('%Y-%m-%d')} to {result.end_date.strftime('%Y-%m-%d')}
💰 Initial Capital: ₹{result.initial_capital:,.0f}
💰 Final Capital: ₹{result.final_capital:,.0f}
📈 Total Return: {result.total_return:+.2%}

📊 Trade Statistics:
   Total Trades: {result.total_trades}
   Winning Trades: {result.winning_trades}
   Losing Trades: {result.losing_trades}
   Win Rate: {result.win_rate:.1%}
   
📈 Performance:
   Average Win: {result.avg_win:+.2%}
   Average Loss: {result.avg_loss:+.2%}
   
🎯 Recent Trades:
""")
        
        # Show last 5 trades
        recent_trades = result.trades[-5:] if len(result.trades) > 5 else result.trades
        for trade in recent_trades:
            status_emoji = "✅" if trade.status == "WIN" else "❌" if trade.status == "LOSS" else "⏰"
            print(f"   {status_emoji} {trade.signal_type.value} {trade.pattern}: {trade.pnl_percent:+.2%}")


def main():
    """Example backtest"""
    print("🔄 RETAIL TRAP MOMENTUM STRATEGY - BACKTEST")
    print("="*60)
    
    backtester = SimpleBacktester(initial_capital=100000)
    
    # Test on a single symbol
    test_symbol = "TATAMOTORS"
    result = backtester.backtest_symbol(test_symbol, days_back=30)
    backtester.print_results(test_symbol, result)


if __name__ == "__main__":
    main()
