#!/usr/bin/env python3
"""
Retail Trap Momentum Strategy - Command Line Interface

Quick and easy way to get trading signals from the command line.

Usage:
    python -m app.trading.cli TATAMOTORS              # Single stock signal
    python -m app.trading.cli RELIANCE TCS INFY       # Multiple stocks
    python -m app.trading.cli --scan                  # Scan popular stocks
    python -m app.trading.cli --timeframe 3m SYMBOL   # Use 3-minute timeframe
"""

import argparse
import sys
import os
from typing import List

# Add parent directories to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.trading.strategy_runner import StrategyRunner
from app.trading.models import SignalType


def main():
    parser = argparse.ArgumentParser(
        description="Retail Trap Momentum Strategy - Get trading signals that capture retail trader emotions",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s TATAMOTORS                    # Get signal for TATAMOTORS
  %(prog)s RELIANCE TCS INFY             # Get signals for multiple stocks  
  %(prog)s --scan                        # Scan popular stocks for signals
  %(prog)s --timeframe 3m TATAMOTORS     # Use 3-minute timeframe
  %(prog)s --days 10 RELIANCE            # Use 10 days of historical data

Strategy Philosophy:
  This strategy captures retail trader emotions by identifying patterns where
  retail traders get trapped by smart money. We position ourselves with the
  smart money, against retail sentiment.

Key Patterns:
  • False Breakouts - Retail chases breakouts, gets trapped
  • Volume Spike Reversals - Retail FOMO creates liquidity for smart money
  • Psychological Level Rejections - Round numbers trap retail traders
  • RSI Divergence - Shows institutional vs retail sentiment
  • FOMO Traps - High RSI + Volume + Resistance = retail trap
        """
    )
    
    parser.add_argument(
        'symbols',
        nargs='*',
        help='Stock symbols to analyze (e.g., TATAMOTORS RELIANCE TCS)'
    )
    
    parser.add_argument(
        '--timeframe', '-t',
        choices=['3m', '5m'],
        default='5m',
        help='Chart timeframe (default: 5m)'
    )
    
    parser.add_argument(
        '--days', '-d',
        type=int,
        default=5,
        help='Days of historical data to analyze (default: 5)'
    )
    
    parser.add_argument(
        '--scan', '-s',
        action='store_true',
        help='Scan popular Indian stocks for signals'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Show detailed analysis'
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.symbols and not args.scan:
        parser.error("Please provide stock symbols or use --scan option")
    
    # Initialize strategy runner
    print(f"🚀 Initializing Retail Trap Momentum Strategy ({args.timeframe} timeframe)")
    runner = StrategyRunner(timeframe=args.timeframe)
    
    if args.scan:
        # Scan popular stocks
        popular_stocks = [
            "ZUARI", "TATACHEM", "PARADEEP", "GODIGIT", "HDFCBANK",
            "ICICIBANK", "BAJFINANCE", "MARUTI", "LT", "WIPRO",
            "SBIN", "KOTAKBANK", "AXISBANK", "BHARTIARTL", "ITC"
        ]
        
        print(f"\n🔍 Scanning {len(popular_stocks)} popular stocks...")
        results = runner.scan_multiple_symbols(popular_stocks, args.days)
        
        # Show summary
        signals = [signal for signal in results.values() if signal is not None]
        if signals:
            print(f"\n🎯 Found {len(signals)} trading signals:")
            for symbol, signal in results.items():
                if signal:
                    emoji = "🟢" if signal.signal_type == SignalType.BUY else "🔴"
                    print(f"   {emoji} {symbol}: {signal.signal_type.value} at ₹{signal.entry_price:.2f} "
                          f"(Confidence: {signal.confidence:.1%})")
        else:
            print("\n⚪ No signals found in the scanned stocks")
    
    else:
        # Analyze specific symbols
        if len(args.symbols) == 1:
            # Single symbol - show detailed analysis
            symbol = args.symbols[0].upper()
            print(f"\n🔍 Analyzing {symbol}...")
            
            if args.verbose:
                # Show detailed state
                state = runner.get_strategy_state(symbol, args.days)
                if state:
                    print(f"""
📊 DETAILED ANALYSIS FOR {symbol}
{'='*40}
Current Price: ₹{state.price_action.current_price:.2f}
Price Change: {state.price_action.price_change_percent:+.2f}%
Market Condition: {state.market_condition.value}
RSI: {state.rsi:.1f} ({state.rsi_trend})
Volume: {state.volume_profile.volume_ratio:.1f}x average

Detected Patterns:""")
                    if state.detected_patterns:
                        for pattern in state.detected_patterns:
                            confidence = state.pattern_confidence.get(pattern.value, 0)
                            print(f"  • {pattern.value.replace('_', ' ').title()}: {confidence:.1%}")
                    else:
                        print("  • No significant patterns detected")
            
            # Get signal
            signal = runner.get_signal(symbol, args.days)
            
        else:
            # Multiple symbols - quick scan
            print(f"\n🔍 Analyzing {len(args.symbols)} symbols...")
            symbols = [s.upper() for s in args.symbols]
            results = runner.scan_multiple_symbols(symbols, args.days)
            
            # Show results
            for symbol, signal in results.items():
                if signal:
                    emoji = "🟢" if signal.signal_type == SignalType.BUY else "🔴"
                    print(f"{emoji} {symbol}: {signal.signal_type.value} at ₹{signal.entry_price:.2f}")
                else:
                    print(f"⚪ {symbol}: No signal")


def quick_signal(symbol: str, timeframe: str = "5m") -> None:
    """Quick function to get a signal for a symbol"""
    runner = StrategyRunner(timeframe=timeframe)
    signal = runner.get_signal(symbol.upper())
    
    if signal:
        emoji = "🟢" if signal.signal_type == SignalType.BUY else "🔴"
        print(f"{emoji} {signal.symbol}: {signal.signal_type.value} at ₹{signal.entry_price:.2f} "
              f"(SL: ₹{signal.stop_loss:.2f}, TP: ₹{signal.take_profit:.2f})")
    else:
        print(f"⚪ {symbol}: No signal")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error: {e}")
        sys.exit(1)
