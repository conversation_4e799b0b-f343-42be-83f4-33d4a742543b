"""
Trading models and data structures
"""
from pydantic import BaseModel
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class SignalType(str, Enum):
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


class SignalStrength(str, Enum):
    WEAK = "WEAK"
    MODERATE = "MODERATE"
    STRONG = "STRONG"


class TradingSignal(BaseModel):
    """Trading signal with entry, stop loss, and take profit"""
    symbol: str
    signal_type: SignalType
    strength: SignalStrength
    confidence: float  # 0.0 to 1.0
    
    # Price levels
    entry_price: float
    stop_loss: float
    take_profit: float
    
    # Risk management
    risk_reward_ratio: float
    position_size_percentage: float  # % of portfolio
    
    # Signal details
    timestamp: datetime
    timeframe: str  # "3m", "5m", etc.
    strategy_name: str
    reasoning: Dict[str, Any]
    
    # Technical levels
    support_level: Optional[float] = None
    resistance_level: Optional[float] = None
    
    # Indicators at signal time
    rsi: Optional[float] = None
    volume_ratio: Optional[float] = None
    price_vs_vwap: Optional[float] = None


class MarketCondition(str, Enum):
    TRENDING_UP = "TRENDING_UP"
    TRENDING_DOWN = "TRENDING_DOWN"
    SIDEWAYS = "SIDEWAYS"
    VOLATILE = "VOLATILE"


class RetailTrapPattern(str, Enum):
    FALSE_BREAKOUT = "FALSE_BREAKOUT"
    VOLUME_SPIKE_REVERSAL = "VOLUME_SPIKE_REVERSAL"
    PSYCHOLOGICAL_LEVEL_REJECTION = "PSYCHOLOGICAL_LEVEL_REJECTION"
    RSI_DIVERGENCE = "RSI_DIVERGENCE"
    FOMO_TRAP = "FOMO_TRAP"


class TechnicalLevel(BaseModel):
    """Support/Resistance level"""
    price: float
    strength: float  # 0.0 to 1.0
    touches: int
    level_type: str  # "support", "resistance", "psychological"
    last_touch: datetime


class VolumeProfile(BaseModel):
    """Volume analysis data"""
    avg_volume_20: float
    current_volume: float
    volume_ratio: float
    volume_spike: bool
    volume_trend: str  # "increasing", "decreasing", "stable"


class PriceAction(BaseModel):
    """Price action analysis"""
    current_price: float
    previous_close: float
    high_of_day: float
    low_of_day: float
    price_change_percent: float
    
    # Candle patterns
    is_doji: bool
    is_hammer: bool
    is_shooting_star: bool
    is_engulfing: bool
    
    # Breakout analysis
    breakout_level: Optional[float] = None
    is_false_breakout: bool = False
    breakout_volume_confirmation: bool = False


class StrategyState(BaseModel):
    """Current state of the trading strategy"""
    symbol: str
    timeframe: str
    last_update: datetime
    market_condition: MarketCondition
    
    # Current levels
    support_levels: List[TechnicalLevel]
    resistance_levels: List[TechnicalLevel]
    
    # Indicators
    rsi: float
    rsi_trend: str
    volume_profile: VolumeProfile
    price_action: PriceAction
    
    # Pattern detection
    detected_patterns: List[RetailTrapPattern]
    pattern_confidence: Dict[str, float]
    
    # Signal history
    last_signal: Optional[TradingSignal] = None
    signal_count_today: int = 0
