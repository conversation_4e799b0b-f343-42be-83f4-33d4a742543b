"""
Strategy Runner for Real-time Trading Signals

This module provides a simple interface to run the Retail Trap Momentum strategy
and get trading signals for any stock symbol.
"""

import sys
import os
from typing import Optional, Dict, Any
from datetime import datetime

# Add parent directories to path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app.trading.strategies.retail_trap_momentum import RetailTrapMomentumStrategy
from app.trading.models import TradingSignal, StrategyState


class StrategyRunner:
    """Main interface for running trading strategies"""
    
    def __init__(self, timeframe: str = "5m"):
        """
        Initialize strategy runner
        
        Args:
            timeframe: Chart timeframe ("3m" or "5m" recommended)
        """
        self.strategy = RetailTrapMomentumStrategy(timeframe=timeframe)
        self.timeframe = timeframe
    
    def get_signal(self, symbol: str, days_back: int = 5) -> Optional[TradingSignal]:
        """
        Get trading signal for a symbol
        
        Args:
            symbol: Stock symbol (e.g., "TATAMOTORS", "RELIANCE")
            days_back: Number of days of historical data to analyze
            
        Returns:
            TradingSignal object with entry, stop loss, take profit, or None if no signal
        """
        try:
            print(f"🔍 Analyzing {symbol} on {self.timeframe} timeframe...")
            
            signal = self.strategy.generate_signal(symbol, days_back)
            
            if signal:
                print(f"✅ Signal generated for {symbol}")
                self._print_signal_details(signal)
            else:
                print(f"⚪ No signal for {symbol} at this time")
            
            return signal
            
        except Exception as e:
            print(f"❌ Error getting signal for {symbol}: {e}")
            return None
    
    def get_strategy_state(self, symbol: str, days_back: int = 5) -> Optional[StrategyState]:
        """
        Get detailed strategy state for analysis
        
        Args:
            symbol: Stock symbol
            days_back: Number of days of historical data
            
        Returns:
            StrategyState with detailed analysis
        """
        try:
            return self.strategy.analyze_symbol(symbol, days_back)
        except Exception as e:
            print(f"Error getting strategy state for {symbol}: {e}")
            return None
    
    def scan_multiple_symbols(self, symbols: list, days_back: int = 5) -> Dict[str, Optional[TradingSignal]]:
        """
        Scan multiple symbols for trading signals
        
        Args:
            symbols: List of stock symbols
            days_back: Number of days of historical data
            
        Returns:
            Dictionary mapping symbol to signal (or None)
        """
        results = {}
        
        print(f"🔍 Scanning {len(symbols)} symbols for trading opportunities...")
        print("=" * 60)
        
        for symbol in symbols:
            try:
                signal = self.get_signal(symbol, days_back)
                results[symbol] = signal
                print("-" * 40)
            except Exception as e:
                print(f"❌ Error scanning {symbol}: {e}")
                results[symbol] = None
        
        # Summary
        signals_found = sum(1 for signal in results.values() if signal is not None)
        print(f"\n📊 Scan complete: {signals_found} signals found out of {len(symbols)} symbols")
        
        return results
    
    def _print_signal_details(self, signal: TradingSignal):
        """Print formatted signal details"""
        print(f"""
🎯 TRADING SIGNAL - {signal.symbol}
{'='*50}
📈 Signal Type: {signal.signal_type.value} ({signal.strength.value})
💪 Confidence: {signal.confidence:.1%}
💰 Entry Price: ₹{signal.entry_price:.2f}
🛑 Stop Loss: ₹{signal.stop_loss:.2f}
🎯 Take Profit: ₹{signal.take_profit:.2f}
📊 Risk:Reward: 1:{signal.risk_reward_ratio:.1f}
💼 Position Size: {signal.position_size_percentage:.1%} of portfolio
⏰ Timeframe: {signal.timeframe}
🧠 Strategy: {signal.strategy_name}

📋 Analysis:
   Pattern: {signal.reasoning.get('pattern', 'N/A')}
   Description: {signal.reasoning.get('description', 'N/A')}
   RSI: {signal.rsi:.1f}
   Volume Ratio: {signal.volume_ratio:.1f}x
   
⚠️  Risk Management:
   - Risk per trade: {abs(signal.entry_price - signal.stop_loss):.2f} ({abs(signal.entry_price - signal.stop_loss)/signal.entry_price*100:.1f}%)
   - Potential reward: {abs(signal.take_profit - signal.entry_price):.2f} ({abs(signal.take_profit - signal.entry_price)/signal.entry_price*100:.1f}%)
""")


def main():
    """Example usage of the strategy runner"""
    
    # Initialize strategy runner for 5-minute timeframe
    runner = StrategyRunner(timeframe="5m")
    
    # Example 1: Get signal for a single stock
    print("Example 1: Single stock analysis")
    signal = runner.get_signal("TATAMOTORS")
    
    print("\n" + "="*60 + "\n")
    
    # Example 2: Scan multiple stocks
    print("Example 2: Multiple stock scan")
    symbols = ["RELIANCE", "TCS", "INFY", "HDFCBANK", "ICICIBANK"]
    results = runner.scan_multiple_symbols(symbols)
    
    # Show only signals found
    signals_found = {symbol: signal for symbol, signal in results.items() if signal is not None}
    if signals_found:
        print(f"\n🎯 SIGNALS SUMMARY:")
        for symbol, signal in signals_found.items():
            print(f"   {symbol}: {signal.signal_type.value} at ₹{signal.entry_price:.2f} (Confidence: {signal.confidence:.1%})")
    else:
        print("\n⚪ No signals found in the scanned symbols")


if __name__ == "__main__":
    main()
