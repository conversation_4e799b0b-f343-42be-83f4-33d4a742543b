"""
Retail Trap Momentum Strategy - Example Usage

This example demonstrates how to use the Retail Trap Momentum strategy
to generate trading signals that capture retail trader emotions and traps.

Strategy Philosophy:
- Retail traders are emotional and predictable
- They chase breakouts, panic at support breaks, and FOMO at highs  
- Smart money uses these patterns to trap retail traders
- We position ourselves with smart money, against retail sentiment

Key Patterns:
1. False Breakouts - When retail traders get excited about breakouts but get trapped
2. Volume Spike Reversals - When retail FOMO creates volume spikes that smart money uses
3. Psychological Level Rejections - Round numbers where retail traders place orders
4. RSI Divergence - When price and RSI disagree (retail vs smart money)
5. FOMO Traps - High RSI + Volume + Near resistance = retail trap
"""

import sys
import os
from datetime import datetime

# Add parent directories to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.trading.strategy_runner import StrategyRunner
from app.trading.models import SignalType


def demonstrate_strategy():
    """Demonstrate the Retail Trap Momentum strategy"""
    
    print("🎯 RETAIL TRAP MOMENTUM STRATEGY DEMO")
    print("="*60)
    print("""
This strategy captures retail trader emotions by identifying:

🔴 FALSE BREAKOUTS
   - Retail traders chase breakouts above resistance
   - Smart money sells into the buying, causing reversal
   - We sell when price fails to sustain breakout

🔴 VOLUME SPIKE REVERSALS  
   - High volume often indicates retail FOMO
   - Smart money uses this liquidity to exit positions
   - We trade against the emotional move

🔴 PSYCHOLOGICAL LEVEL REJECTIONS
   - Retail traders love round numbers (100, 200, 500, etc.)
   - These become strong support/resistance levels
   - Rejections at these levels offer high-probability trades

🔴 RSI DIVERGENCE
   - Price makes new highs but RSI doesn't = smart money selling
   - Price makes new lows but RSI doesn't = smart money buying
   - Divergence shows institutional vs retail sentiment

🔴 FOMO TRAPS
   - High RSI + Volume spike + Near resistance = retail trap
   - Retail traders buy at the worst possible time
   - We sell into their buying pressure
""")
    
    # Initialize strategy for 5-minute timeframe
    print("\n🚀 Initializing strategy for 5-minute timeframe...")
    runner = StrategyRunner(timeframe="5m")
    
    # Test with popular Indian stocks
    test_symbols = [
        "TATAMOTORS",  # High volatility, good for retail traps
        "RELIANCE",    # Large cap with psychological levels
        "INFY",        # Tech stock with retail following
        "HDFCBANK",    # Banking sector favorite
        "ICICIBANK",   # Another banking favorite
        "TCS",         # IT sector leader
        "BAJFINANCE",  # NBFC with high retail interest
        "MARUTI",      # Auto sector with retail appeal
        "LT",          # Infrastructure play
        "WIPRO"        # IT stock with retail following
    ]
    
    print(f"\n🔍 Scanning {len(test_symbols)} popular stocks for retail trap opportunities...")
    print("="*60)
    
    # Scan for signals
    results = runner.scan_multiple_symbols(test_symbols, days_back=7)
    
    # Analyze results
    buy_signals = []
    sell_signals = []
    
    for symbol, signal in results.items():
        if signal:
            if signal.signal_type == SignalType.BUY:
                buy_signals.append((symbol, signal))
            elif signal.signal_type == SignalType.SELL:
                sell_signals.append((symbol, signal))
    
    # Display summary
    print(f"\n📊 SCAN RESULTS SUMMARY")
    print("="*40)
    print(f"🟢 BUY Signals: {len(buy_signals)}")
    print(f"🔴 SELL Signals: {len(sell_signals)}")
    print(f"⚪ No Signals: {len(test_symbols) - len(buy_signals) - len(sell_signals)}")
    
    # Show detailed signals
    if buy_signals:
        print(f"\n🟢 BUY SIGNALS (Retail Selling, Smart Money Buying)")
        print("-"*50)
        for symbol, signal in buy_signals:
            print(f"""
{symbol}: BUY at ₹{signal.entry_price:.2f}
   Pattern: {signal.reasoning.get('pattern', 'N/A')}
   Confidence: {signal.confidence:.1%}
   Stop Loss: ₹{signal.stop_loss:.2f} ({((signal.stop_loss - signal.entry_price)/signal.entry_price*100):+.1f}%)
   Take Profit: ₹{signal.take_profit:.2f} ({((signal.take_profit - signal.entry_price)/signal.entry_price*100):+.1f}%)
   Risk:Reward: 1:{signal.risk_reward_ratio:.1f}
   RSI: {signal.rsi:.1f} | Volume: {signal.volume_ratio:.1f}x avg
""")
    
    if sell_signals:
        print(f"\n🔴 SELL SIGNALS (Retail Buying, Smart Money Selling)")
        print("-"*50)
        for symbol, signal in sell_signals:
            print(f"""
{symbol}: SELL at ₹{signal.entry_price:.2f}
   Pattern: {signal.reasoning.get('pattern', 'N/A')}
   Confidence: {signal.confidence:.1%}
   Stop Loss: ₹{signal.stop_loss:.2f} ({((signal.stop_loss - signal.entry_price)/signal.entry_price*100):+.1f}%)
   Take Profit: ₹{signal.take_profit:.2f} ({((signal.take_profit - signal.entry_price)/signal.entry_price*100):+.1f}%)
   Risk:Reward: 1:{signal.risk_reward_ratio:.1f}
   RSI: {signal.rsi:.1f} | Volume: {signal.volume_ratio:.1f}x avg
""")
    
    # Trading tips
    print(f"\n💡 TRADING TIPS FOR RETAIL TRAP STRATEGY")
    print("="*50)
    print("""
1. ⏰ TIMING IS CRUCIAL
   - Best results on 3m and 5m timeframes
   - Trade during market hours when retail activity is high
   - Avoid low-volume periods (lunch time, end of day)

2. 🎯 ENTRY DISCIPLINE  
   - Wait for clear pattern confirmation
   - Don't chase - let the setup come to you
   - Higher confidence signals (>70%) are more reliable

3. 🛑 RISK MANAGEMENT
   - Always use stop losses - retail traps can reverse quickly
   - Position size based on confidence and risk-reward ratio
   - Never risk more than 2% per trade

4. 📊 VOLUME CONFIRMATION
   - High volume spikes often indicate retail emotion
   - Low volume breakouts are more likely to be false
   - Watch for volume divergence with price

5. 🧠 PSYCHOLOGY
   - Remember: we're trading against retail emotion
   - When everyone is buying (FOMO), consider selling
   - When everyone is panicking, consider buying
   - Stay disciplined and unemotional

6. ⚡ QUICK EXECUTION
   - These setups can move fast on short timeframes
   - Have your orders ready before the signal
   - Use limit orders to avoid slippage
""")
    
    print(f"\n✅ Demo complete! Strategy is ready for live trading.")
    print(f"📅 Analysis performed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


def test_single_stock_analysis():
    """Test detailed analysis of a single stock"""
    
    print("\n" + "="*60)
    print("🔬 DETAILED SINGLE STOCK ANALYSIS")
    print("="*60)
    
    runner = StrategyRunner(timeframe="5m")
    
    # Test with a popular stock
    test_symbol = "TATAMOTORS"
    print(f"\n📊 Analyzing {test_symbol} in detail...")
    
    # Get strategy state for detailed analysis
    state = runner.get_strategy_state(test_symbol, days_back=7)
    
    if state:
        print(f"""
📈 MARKET ANALYSIS FOR {test_symbol}
{'='*40}
Current Price: ₹{state.price_action.current_price:.2f}
Market Condition: {state.market_condition.value}
RSI: {state.rsi:.1f} ({state.rsi_trend})
Volume Ratio: {state.volume_profile.volume_ratio:.1f}x average

🎯 DETECTED PATTERNS:
""")
        if state.detected_patterns:
            for pattern in state.detected_patterns:
                confidence = state.pattern_confidence.get(pattern.value, 0)
                print(f"   • {pattern.value}: {confidence:.1%} confidence")
        else:
            print("   • No significant patterns detected")
        
        print(f"""
📊 TECHNICAL LEVELS:
Resistance Levels: {len(state.resistance_levels)} found
""")
        for i, level in enumerate(state.resistance_levels[:3]):
            print(f"   R{i+1}: ₹{level.price:.2f} (Strength: {level.strength:.1f}, Touches: {level.touches})")
        
        print(f"Support Levels: {len(state.support_levels)} found")
        for i, level in enumerate(state.support_levels[:3]):
            print(f"   S{i+1}: ₹{level.price:.2f} (Strength: {level.strength:.1f}, Touches: {level.touches})")
        
        # Get signal
        signal = runner.get_signal(test_symbol, days_back=7)
        if signal:
            print(f"\n🎯 TRADING RECOMMENDATION: {signal.signal_type.value}")
        else:
            print(f"\n⚪ NO TRADING SIGNAL AT THIS TIME")
    else:
        print(f"❌ Could not analyze {test_symbol} - insufficient data or API error")


if __name__ == "__main__":
    # Run the demonstration
    demonstrate_strategy()
    
    # Test detailed single stock analysis
    test_single_stock_analysis()
