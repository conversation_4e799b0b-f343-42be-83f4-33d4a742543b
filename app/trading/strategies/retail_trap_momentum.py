"""
Retail Trap Momentum Strategy

This strategy captures retail trader emotions and traps by identifying:
1. False breakouts where retail traders get trapped
2. Volume spikes with price reversals (FOMO traps)
3. Psychological level rejections (round numbers)
4. RSI divergence patterns
5. Support/resistance level manipulation

Designed for 3m and 5m timeframes to catch quick retail moves.
"""

import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Any, Tuple
from datetime import datetime, timedelta
import sys
import os

# Add parent directories to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.trading.models import (
    TradingSignal, SignalType, SignalStrength, MarketCondition,
    RetailTrapPattern, TechnicalLevel, VolumeProfile, PriceAction, StrategyState
)
from app.data.strike_api import StrikeAPI, PriceTick
from app.trading.strategies.signal_generator import RetailTrapSignalGenerator


class RetailTrapMomentumStrategy:
    """
    Retail Trap Momentum Strategy
    
    Core Philosophy:
    - Retail traders are emotional and predictable
    - They chase breakouts, panic at support breaks, and FOMO at highs
    - Smart money uses these patterns to trap retail traders
    - We position ourselves with smart money, against retail sentiment
    """
    
    def __init__(self, timeframe: str = "5m"):
        self.name = "Retail Trap Momentum"
        self.timeframe = timeframe
        self.strike_api = StrikeAPI()
        self.signal_generator = RetailTrapSignalGenerator(self)

        # Strategy parameters
        self.min_volume_spike = 2.0  # 2x average volume for spike
        self.rsi_overbought = 75
        self.rsi_oversold = 25
        self.false_breakout_threshold = 0.5  # % beyond level for false breakout
        self.min_confidence = 0.65

        # Risk management
        self.max_risk_per_trade = 0.02  # 2% risk per trade
        self.min_risk_reward = 2.0  # Minimum 1:2 risk-reward

        # Pattern detection settings
        self.psychological_levels = [50, 100, 150, 200, 250, 300, 500, 1000]  # Round numbers
        self.support_resistance_lookback = 20  # Candles to look back for S/R
        
    def analyze_symbol(self, symbol: str, days_back: int = 5) -> Optional[StrategyState]:
        """
        Analyze a symbol and return current strategy state
        """
        try:
            # Get price data
            price_data = self.strike_api.get_price_data_simple(symbol, days_back)
            if not price_data or len(price_data) < 50:
                return None
            
            # Convert to DataFrame
            df = self._create_dataframe(price_data)
            if df is None or len(df) < 50:
                return None
            
            # Calculate indicators
            df = self._calculate_indicators(df)
            
            # Detect support/resistance levels
            support_levels, resistance_levels = self._detect_support_resistance(df)
            
            # Analyze volume profile
            volume_profile = self._analyze_volume(df)
            
            # Analyze price action
            price_action = self._analyze_price_action(df)
            
            # Detect retail trap patterns
            patterns, pattern_confidence = self._detect_retail_patterns(df, support_levels, resistance_levels)
            
            # Determine market condition
            market_condition = self._determine_market_condition(df)
            
            # Create strategy state
            state = StrategyState(
                symbol=symbol,
                timeframe=self.timeframe,
                last_update=datetime.now(),
                market_condition=market_condition,
                support_levels=support_levels,
                resistance_levels=resistance_levels,
                rsi=df['rsi'].iloc[-1],
                rsi_trend=self._get_rsi_trend(df),
                volume_profile=volume_profile,
                price_action=price_action,
                detected_patterns=patterns,
                pattern_confidence=pattern_confidence,
                signal_count_today=0
            )
            
            return state
            
        except Exception as e:
            print(f"Error analyzing {symbol}: {e}")
            return None
    
    def generate_signal(self, symbol: str, days_back: int = 5) -> Optional[TradingSignal]:
        """
        Generate trading signal based on retail trap patterns
        """
        try:
            # Get strategy state
            state = self.analyze_symbol(symbol, days_back)
            if not state:
                return None
            
            # Get current price data
            price_data = self.strike_api.get_price_data_simple(symbol, days_back)
            df = self._create_dataframe(price_data)
            df = self._calculate_indicators(df)
            
            current_price = df['close'].iloc[-1]
            
            # Check for signal patterns using signal generator
            signal = None

            # Pattern 1: False Breakout Reversal
            signal = self.signal_generator.check_false_breakout_signal(df, state, current_price)
            if signal:
                return signal

            # Pattern 2: Volume Spike Reversal
            signal = self.signal_generator.check_volume_spike_reversal(df, state, current_price)
            if signal:
                return signal

            # Pattern 3: Psychological Level Rejection
            signal = self.signal_generator.check_psychological_level_signal(df, state, current_price)
            if signal:
                return signal

            # Pattern 4: RSI Divergence
            signal = self.signal_generator.check_rsi_divergence_signal(df, state, current_price)
            if signal:
                return signal
            
            return None
            
        except Exception as e:
            print(f"Error generating signal for {symbol}: {e}")
            return None
    
    def _create_dataframe(self, price_data: List[PriceTick]) -> Optional[pd.DataFrame]:
        """Convert price data to DataFrame"""
        try:
            data = []
            for tick in price_data:
                data.append({
                    'datetime': pd.to_datetime(tick.datetime),
                    'open': tick.open,
                    'high': tick.high,
                    'low': tick.low,
                    'close': tick.close,
                    'volume': tick.volume
                })
            
            df = pd.DataFrame(data)
            df.set_index('datetime', inplace=True)
            df.sort_index(inplace=True)
            return df
            
        except Exception as e:
            print(f"Error creating dataframe: {e}")
            return None
    
    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators"""
        try:
            # RSI
            df['rsi'] = self._calculate_rsi(df['close'])
            
            # Moving averages
            df['sma_20'] = df['close'].rolling(window=20).mean()
            df['ema_9'] = df['close'].ewm(span=9).mean()
            df['ema_21'] = df['close'].ewm(span=21).mean()
            
            # Volume indicators
            df['volume_sma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            
            # VWAP
            df['vwap'] = (df['close'] * df['volume']).cumsum() / df['volume'].cumsum()
            
            # Bollinger Bands
            bb_period = 20
            bb_std = 2
            df['bb_middle'] = df['close'].rolling(window=bb_period).mean()
            bb_std_dev = df['close'].rolling(window=bb_period).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std_dev * bb_std)
            df['bb_lower'] = df['bb_middle'] - (bb_std_dev * bb_std)
            
            # Price position in BB
            df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            return df
            
        except Exception as e:
            print(f"Error calculating indicators: {e}")
            return df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def _detect_support_resistance(self, df: pd.DataFrame) -> Tuple[List[TechnicalLevel], List[TechnicalLevel]]:
        """Detect support and resistance levels"""
        support_levels = []
        resistance_levels = []

        try:
            # Look for swing highs and lows
            highs = df['high'].rolling(window=5, center=True).max()
            lows = df['low'].rolling(window=5, center=True).min()

            # Find swing points
            swing_highs = df[df['high'] == highs]['high'].dropna()
            swing_lows = df[df['low'] == lows]['low'].dropna()

            # Group similar levels (within 0.5%)
            for high in swing_highs.values:
                similar_levels = [h for h in swing_highs.values if abs(h - high) / high < 0.005]
                if len(similar_levels) >= 2:  # At least 2 touches
                    resistance_levels.append(TechnicalLevel(
                        price=high,
                        strength=len(similar_levels) / 10.0,  # Normalize strength
                        touches=len(similar_levels),
                        level_type="resistance",
                        last_touch=datetime.now()
                    ))

            for low in swing_lows.values:
                similar_levels = [l for l in swing_lows.values if abs(l - low) / low < 0.005]
                if len(similar_levels) >= 2:  # At least 2 touches
                    support_levels.append(TechnicalLevel(
                        price=low,
                        strength=len(similar_levels) / 10.0,  # Normalize strength
                        touches=len(similar_levels),
                        level_type="support",
                        last_touch=datetime.now()
                    ))

            # Add psychological levels
            current_price = df['close'].iloc[-1]
            for level in self.psychological_levels:
                if abs(current_price - level) / current_price < 0.1:  # Within 10%
                    if level > current_price:
                        resistance_levels.append(TechnicalLevel(
                            price=level,
                            strength=0.7,
                            touches=1,
                            level_type="psychological",
                            last_touch=datetime.now()
                        ))
                    else:
                        support_levels.append(TechnicalLevel(
                            price=level,
                            strength=0.7,
                            touches=1,
                            level_type="psychological",
                            last_touch=datetime.now()
                        ))

            # Remove duplicates and sort
            support_levels = sorted(list(set(support_levels)), key=lambda x: x.price, reverse=True)[:5]
            resistance_levels = sorted(list(set(resistance_levels)), key=lambda x: x.price)[:5]

        except Exception as e:
            print(f"Error detecting support/resistance: {e}")

        return support_levels, resistance_levels

    def _analyze_volume(self, df: pd.DataFrame) -> VolumeProfile:
        """Analyze volume patterns"""
        try:
            current_volume = df['volume'].iloc[-1]
            avg_volume_20 = df['volume'].rolling(window=20).mean().iloc[-1]
            volume_ratio = current_volume / avg_volume_20

            # Determine volume trend
            recent_volumes = df['volume'].tail(5).values
            volume_trend = "stable"
            if recent_volumes[-1] > recent_volumes[0] * 1.2:
                volume_trend = "increasing"
            elif recent_volumes[-1] < recent_volumes[0] * 0.8:
                volume_trend = "decreasing"

            return VolumeProfile(
                avg_volume_20=avg_volume_20,
                current_volume=current_volume,
                volume_ratio=volume_ratio,
                volume_spike=volume_ratio > self.min_volume_spike,
                volume_trend=volume_trend
            )

        except Exception as e:
            print(f"Error analyzing volume: {e}")
            return VolumeProfile(
                avg_volume_20=0,
                current_volume=0,
                volume_ratio=1.0,
                volume_spike=False,
                volume_trend="stable"
            )

    def _analyze_price_action(self, df: pd.DataFrame) -> PriceAction:
        """Analyze current price action"""
        try:
            current = df.iloc[-1]
            previous = df.iloc[-2] if len(df) > 1 else current

            # Calculate price change
            price_change_percent = ((current['close'] - previous['close']) / previous['close']) * 100

            # Candle pattern analysis
            body_size = abs(current['close'] - current['open'])
            total_range = current['high'] - current['low']
            upper_shadow = current['high'] - max(current['open'], current['close'])
            lower_shadow = min(current['open'], current['close']) - current['low']

            # Pattern detection
            is_doji = body_size < (total_range * 0.1) if total_range > 0 else False
            is_hammer = (lower_shadow > body_size * 2) and (upper_shadow < body_size * 0.5)
            is_shooting_star = (upper_shadow > body_size * 2) and (lower_shadow < body_size * 0.5)
            is_engulfing = False  # Would need previous candle analysis

            return PriceAction(
                current_price=current['close'],
                previous_close=previous['close'],
                high_of_day=current['high'],
                low_of_day=current['low'],
                price_change_percent=price_change_percent,
                is_doji=is_doji,
                is_hammer=is_hammer,
                is_shooting_star=is_shooting_star,
                is_engulfing=is_engulfing,
                is_false_breakout=False,  # Will be determined by pattern detection
                breakout_volume_confirmation=False
            )

        except Exception as e:
            print(f"Error analyzing price action: {e}")
            return PriceAction(
                current_price=0,
                previous_close=0,
                high_of_day=0,
                low_of_day=0,
                price_change_percent=0,
                is_doji=False,
                is_hammer=False,
                is_shooting_star=False,
                is_engulfing=False,
                is_false_breakout=False,
                breakout_volume_confirmation=False
            )

    def _detect_retail_patterns(self, df: pd.DataFrame, support_levels: List[TechnicalLevel],
                               resistance_levels: List[TechnicalLevel]) -> Tuple[List[RetailTrapPattern], Dict[str, float]]:
        """Detect retail trap patterns"""
        patterns = []
        confidence = {}

        try:
            current_price = df['close'].iloc[-1]
            current_rsi = df['rsi'].iloc[-1]
            volume_ratio = df['volume_ratio'].iloc[-1]

            # Pattern 1: False Breakout Detection
            false_breakout_conf = self._detect_false_breakout(df, resistance_levels, support_levels)
            if false_breakout_conf > 0.6:
                patterns.append(RetailTrapPattern.FALSE_BREAKOUT)
                confidence["FALSE_BREAKOUT"] = false_breakout_conf

            # Pattern 2: Volume Spike Reversal
            volume_reversal_conf = self._detect_volume_spike_reversal(df)
            if volume_reversal_conf > 0.6:
                patterns.append(RetailTrapPattern.VOLUME_SPIKE_REVERSAL)
                confidence["VOLUME_SPIKE_REVERSAL"] = volume_reversal_conf

            # Pattern 3: Psychological Level Rejection
            psych_rejection_conf = self._detect_psychological_rejection(df)
            if psych_rejection_conf > 0.6:
                patterns.append(RetailTrapPattern.PSYCHOLOGICAL_LEVEL_REJECTION)
                confidence["PSYCHOLOGICAL_LEVEL_REJECTION"] = psych_rejection_conf

            # Pattern 4: RSI Divergence
            rsi_div_conf = self._detect_rsi_divergence(df)
            if rsi_div_conf > 0.6:
                patterns.append(RetailTrapPattern.RSI_DIVERGENCE)
                confidence["RSI_DIVERGENCE"] = rsi_div_conf

            # Pattern 5: FOMO Trap (High RSI + Volume Spike + Near Resistance)
            fomo_conf = self._detect_fomo_trap(df, resistance_levels)
            if fomo_conf > 0.6:
                patterns.append(RetailTrapPattern.FOMO_TRAP)
                confidence["FOMO_TRAP"] = fomo_conf

        except Exception as e:
            print(f"Error detecting patterns: {e}")

        return patterns, confidence

    def _detect_false_breakout(self, df: pd.DataFrame, resistance_levels: List[TechnicalLevel],
                              support_levels: List[TechnicalLevel]) -> float:
        """Detect false breakout patterns"""
        try:
            if len(df) < 10:
                return 0.0

            current_price = df['close'].iloc[-1]
            recent_high = df['high'].tail(5).max()
            recent_low = df['low'].tail(5).min()

            confidence = 0.0

            # Check for resistance breakout failure
            for resistance in resistance_levels:
                if (recent_high > resistance.price * 1.002 and  # Broke above resistance
                    current_price < resistance.price * 0.998):  # But closed back below
                    confidence += 0.3 * resistance.strength

            # Check for support breakdown failure
            for support in support_levels:
                if (recent_low < support.price * 0.998 and  # Broke below support
                    current_price > support.price * 1.002):  # But closed back above
                    confidence += 0.3 * support.strength

            # Volume confirmation (low volume on breakout = more likely false)
            volume_ratio = df['volume_ratio'].iloc[-1]
            if volume_ratio < 1.5:  # Low volume breakout
                confidence += 0.2

            return min(confidence, 1.0)

        except Exception as e:
            print(f"Error detecting false breakout: {e}")
            return 0.0

    def _detect_volume_spike_reversal(self, df: pd.DataFrame) -> float:
        """Detect volume spike with price reversal"""
        try:
            if len(df) < 5:
                return 0.0

            current_volume_ratio = df['volume_ratio'].iloc[-1]
            current_price = df['close'].iloc[-1]
            prev_price = df['close'].iloc[-2]

            confidence = 0.0

            # High volume spike
            if current_volume_ratio > self.min_volume_spike:
                confidence += 0.4

                # Price reversal after spike
                price_change = (current_price - prev_price) / prev_price
                if abs(price_change) > 0.01:  # Significant price move
                    # Check if it's reversing from recent trend
                    recent_trend = (df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5]
                    if (recent_trend > 0 and price_change < 0) or (recent_trend < 0 and price_change > 0):
                        confidence += 0.4

            return confidence

        except Exception as e:
            print(f"Error detecting volume spike reversal: {e}")
            return 0.0

    def _detect_psychological_rejection(self, df: pd.DataFrame) -> float:
        """Detect rejection at psychological levels"""
        try:
            current_price = df['close'].iloc[-1]
            recent_high = df['high'].tail(3).max()
            recent_low = df['low'].tail(3).min()

            confidence = 0.0

            # Check proximity to round numbers
            for level in self.psychological_levels:
                # Rejection at resistance (round number above)
                if (level > current_price and
                    recent_high >= level * 0.999 and
                    current_price < level * 0.995):
                    distance_factor = 1 - abs(current_price - level) / level
                    confidence += 0.5 * distance_factor

                # Rejection at support (round number below)
                if (level < current_price and
                    recent_low <= level * 1.001 and
                    current_price > level * 1.005):
                    distance_factor = 1 - abs(current_price - level) / level
                    confidence += 0.5 * distance_factor

            return min(confidence, 1.0)

        except Exception as e:
            print(f"Error detecting psychological rejection: {e}")
            return 0.0

    def _detect_rsi_divergence(self, df: pd.DataFrame) -> float:
        """Detect RSI divergence patterns"""
        try:
            if len(df) < 20:
                return 0.0

            # Get recent price and RSI data
            recent_prices = df['close'].tail(10)
            recent_rsi = df['rsi'].tail(10)

            confidence = 0.0

            # Bullish divergence: Price makes lower low, RSI makes higher low
            price_low_idx = recent_prices.idxmin()
            rsi_at_price_low = recent_rsi.loc[price_low_idx]

            # Check if there's a previous low to compare
            earlier_prices = df['close'].iloc[-20:-10]
            if len(earlier_prices) > 0:
                earlier_low_idx = earlier_prices.idxmin()
                earlier_rsi = df['rsi'].loc[earlier_low_idx]

                # Bullish divergence
                if (recent_prices.min() < earlier_prices.min() and
                    rsi_at_price_low > earlier_rsi):
                    confidence += 0.6

                # Bearish divergence: Price makes higher high, RSI makes lower high
                price_high_idx = recent_prices.idxmax()
                rsi_at_price_high = recent_rsi.loc[price_high_idx]
                earlier_high_idx = earlier_prices.idxmax()
                earlier_rsi_high = df['rsi'].loc[earlier_high_idx]

                if (recent_prices.max() > earlier_prices.max() and
                    rsi_at_price_high < earlier_rsi_high):
                    confidence += 0.6

            return min(confidence, 1.0)

        except Exception as e:
            print(f"Error detecting RSI divergence: {e}")
            return 0.0

    def _detect_fomo_trap(self, df: pd.DataFrame, resistance_levels: List[TechnicalLevel]) -> float:
        """Detect FOMO trap patterns"""
        try:
            current_rsi = df['rsi'].iloc[-1]
            current_price = df['close'].iloc[-1]
            volume_ratio = df['volume_ratio'].iloc[-1]

            confidence = 0.0

            # High RSI (overbought)
            if current_rsi > self.rsi_overbought:
                confidence += 0.3

            # High volume (FOMO buying)
            if volume_ratio > self.min_volume_spike:
                confidence += 0.3

            # Near resistance level
            for resistance in resistance_levels:
                distance_to_resistance = abs(current_price - resistance.price) / resistance.price
                if distance_to_resistance < 0.02:  # Within 2%
                    confidence += 0.4 * resistance.strength
                    break

            return min(confidence, 1.0)

        except Exception as e:
            print(f"Error detecting FOMO trap: {e}")
            return 0.0

    def _determine_market_condition(self, df: pd.DataFrame) -> MarketCondition:
        """Determine current market condition"""
        try:
            # Use EMA crossover and price action
            ema_9 = df['ema_9'].iloc[-1]
            ema_21 = df['ema_21'].iloc[-1]
            current_price = df['close'].iloc[-1]

            # Calculate recent volatility
            recent_returns = df['close'].pct_change().tail(20)
            volatility = recent_returns.std()

            if volatility > 0.03:  # High volatility threshold
                return MarketCondition.VOLATILE
            elif ema_9 > ema_21 and current_price > ema_9:
                return MarketCondition.TRENDING_UP
            elif ema_9 < ema_21 and current_price < ema_9:
                return MarketCondition.TRENDING_DOWN
            else:
                return MarketCondition.SIDEWAYS

        except Exception as e:
            print(f"Error determining market condition: {e}")
            return MarketCondition.SIDEWAYS

    def _get_rsi_trend(self, df: pd.DataFrame) -> str:
        """Get RSI trend direction"""
        try:
            recent_rsi = df['rsi'].tail(5)
            if recent_rsi.iloc[-1] > recent_rsi.iloc[0]:
                return "rising"
            elif recent_rsi.iloc[-1] < recent_rsi.iloc[0]:
                return "falling"
            else:
                return "sideways"
        except:
            return "sideways"
