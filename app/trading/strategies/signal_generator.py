"""
Signal Generation Methods for Retail Trap Momentum Strategy
"""

import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Any
from datetime import datetime
import sys
import os

# Add parent directories to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.trading.models import (
    TradingSignal, SignalType, SignalStrength, StrategyState, TechnicalLevel
)


class RetailTrapSignalGenerator:
    """Generate trading signals based on retail trap patterns"""
    
    def __init__(self, strategy_instance):
        self.strategy = strategy_instance
    
    def check_false_breakout_signal(self, df: pd.DataFrame, state: StrategyState, 
                                   current_price: float) -> Optional[TradingSignal]:
        """Check for false breakout reversal signals"""
        try:
            if not state.detected_patterns or "FALSE_BREAKOUT" not in [p.value for p in state.detected_patterns]:
                return None
            
            confidence = state.pattern_confidence.get("FALSE_BREAKOUT", 0)
            if confidence < 0.50:  # Even lower threshold for more signals
                return None
            
            # Determine signal direction
            signal_type = None
            entry_price = current_price
            
            # False breakout above resistance -> SELL signal
            for resistance in state.resistance_levels:
                if (current_price < resistance.price and 
                    df['high'].tail(3).max() > resistance.price):
                    signal_type = SignalType.SELL
                    stop_loss = resistance.price * 1.01  # 1% above resistance
                    take_profit = current_price - (stop_loss - current_price) * 2  # 1:2 RR
                    break
            
            # False breakout below support -> BUY signal
            if not signal_type:
                for support in state.support_levels:
                    if (current_price > support.price and 
                        df['low'].tail(3).min() < support.price):
                        signal_type = SignalType.BUY
                        stop_loss = support.price * 0.99  # 1% below support
                        take_profit = current_price + (current_price - stop_loss) * 2  # 1:2 RR
                        break
            
            if not signal_type:
                return None
            
            # Calculate risk-reward ratio
            risk = abs(entry_price - stop_loss)
            reward = abs(take_profit - entry_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            if risk_reward_ratio < self.strategy.min_risk_reward:
                return None
            
            # Determine signal strength
            strength = SignalStrength.STRONG if confidence > 0.8 else SignalStrength.MODERATE
            
            return TradingSignal(
                symbol=state.symbol,
                signal_type=signal_type,
                strength=strength,
                confidence=confidence,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                risk_reward_ratio=risk_reward_ratio,
                position_size_percentage=self._calculate_position_size(risk_reward_ratio, confidence),
                timestamp=datetime.now(),
                timeframe=state.timeframe,
                strategy_name=self.strategy.name,
                reasoning={
                    "pattern": "False Breakout Reversal",
                    "description": f"Price failed to sustain breakout, reversing back into range",
                    "rsi": state.rsi,
                    "volume_ratio": state.volume_profile.volume_ratio,
                    "market_condition": state.market_condition.value
                },
                rsi=state.rsi,
                volume_ratio=state.volume_profile.volume_ratio
            )
            
        except Exception as e:
            print(f"Error checking false breakout signal: {e}")
            return None
    
    def check_volume_spike_reversal(self, df: pd.DataFrame, state: StrategyState, 
                                   current_price: float) -> Optional[TradingSignal]:
        """Check for volume spike reversal signals"""
        try:
            if not state.volume_profile.volume_spike:
                return None
            
            if "VOLUME_SPIKE_REVERSAL" not in [p.value for p in state.detected_patterns]:
                return None
            
            confidence = state.pattern_confidence.get("VOLUME_SPIKE_REVERSAL", 0)
            if confidence < 0.50:  # Lower threshold
                return None
            
            # Determine reversal direction
            recent_trend = (df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5]
            price_change_today = state.price_action.price_change_percent
            
            signal_type = None
            
            # Volume spike with bearish reversal (sell signal)
            if recent_trend > 0.02 and price_change_today < -0.01:  # Was trending up, now reversing down
                signal_type = SignalType.SELL
                stop_loss = current_price * 1.02  # 2% above current price
                take_profit = current_price * 0.96  # 4% below current price
            
            # Volume spike with bullish reversal (buy signal)
            elif recent_trend < -0.02 and price_change_today > 0.01:  # Was trending down, now reversing up
                signal_type = SignalType.BUY
                stop_loss = current_price * 0.98  # 2% below current price
                take_profit = current_price * 1.04  # 4% above current price
            
            if not signal_type:
                return None
            
            # Calculate risk-reward
            risk = abs(current_price - stop_loss)
            reward = abs(take_profit - current_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            if risk_reward_ratio < self.strategy.min_risk_reward:
                return None
            
            strength = SignalStrength.STRONG if state.volume_profile.volume_ratio > 3.0 else SignalStrength.MODERATE
            
            return TradingSignal(
                symbol=state.symbol,
                signal_type=signal_type,
                strength=strength,
                confidence=confidence,
                entry_price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                risk_reward_ratio=risk_reward_ratio,
                position_size_percentage=self._calculate_position_size(risk_reward_ratio, confidence),
                timestamp=datetime.now(),
                timeframe=state.timeframe,
                strategy_name=self.strategy.name,
                reasoning={
                    "pattern": "Volume Spike Reversal",
                    "description": f"High volume ({state.volume_profile.volume_ratio:.1f}x avg) with price reversal",
                    "volume_ratio": state.volume_profile.volume_ratio,
                    "price_change": price_change_today,
                    "recent_trend": recent_trend * 100
                },
                rsi=state.rsi,
                volume_ratio=state.volume_profile.volume_ratio
            )
            
        except Exception as e:
            print(f"Error checking volume spike reversal: {e}")
            return None
    
    def check_psychological_level_signal(self, df: pd.DataFrame, state: StrategyState, 
                                        current_price: float) -> Optional[TradingSignal]:
        """Check for psychological level rejection signals"""
        try:
            if "PSYCHOLOGICAL_LEVEL_REJECTION" not in [p.value for p in state.detected_patterns]:
                return None
            
            confidence = state.pattern_confidence.get("PSYCHOLOGICAL_LEVEL_REJECTION", 0)
            if confidence < 0.50:  # Lower threshold
                return None
            
            # Find the relevant psychological level
            relevant_level = None
            signal_type = None
            
            for level in self.strategy.psychological_levels:
                distance = abs(current_price - level) / current_price
                if distance < 0.02:  # Within 2%
                    relevant_level = level
                    
                    # Rejection from above (resistance) -> SELL
                    if current_price < level and df['high'].tail(3).max() >= level * 0.999:
                        signal_type = SignalType.SELL
                        stop_loss = level * 1.005
                        take_profit = current_price - (stop_loss - current_price) * 2.5
                    
                    # Rejection from below (support) -> BUY
                    elif current_price > level and df['low'].tail(3).min() <= level * 1.001:
                        signal_type = SignalType.BUY
                        stop_loss = level * 0.995
                        take_profit = current_price + (current_price - stop_loss) * 2.5
                    
                    break
            
            if not signal_type or not relevant_level:
                return None
            
            # Calculate risk-reward
            risk = abs(current_price - stop_loss)
            reward = abs(take_profit - current_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            if risk_reward_ratio < self.strategy.min_risk_reward:
                return None
            
            return TradingSignal(
                symbol=state.symbol,
                signal_type=signal_type,
                strength=SignalStrength.MODERATE,
                confidence=confidence,
                entry_price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                risk_reward_ratio=risk_reward_ratio,
                position_size_percentage=self._calculate_position_size(risk_reward_ratio, confidence),
                timestamp=datetime.now(),
                timeframe=state.timeframe,
                strategy_name=self.strategy.name,
                reasoning={
                    "pattern": "Psychological Level Rejection",
                    "description": f"Rejection at psychological level {relevant_level}",
                    "psychological_level": relevant_level,
                    "distance_to_level": abs(current_price - relevant_level),
                    "rsi": state.rsi
                },
                rsi=state.rsi,
                volume_ratio=state.volume_profile.volume_ratio
            )
            
        except Exception as e:
            print(f"Error checking psychological level signal: {e}")
            return None
    
    def check_rsi_divergence_signal(self, df: pd.DataFrame, state: StrategyState, 
                                   current_price: float) -> Optional[TradingSignal]:
        """Check for RSI divergence signals"""
        try:
            if "RSI_DIVERGENCE" not in [p.value for p in state.detected_patterns]:
                return None
            
            confidence = state.pattern_confidence.get("RSI_DIVERGENCE", 0)
            if confidence < 0.50:  # Lower threshold
                return None
            
            # Determine divergence type
            recent_prices = df['close'].tail(10)
            recent_rsi = df['rsi'].tail(10)
            
            signal_type = None
            
            # Bullish divergence: Price lower low, RSI higher low
            if (recent_prices.iloc[-1] < recent_prices.iloc[0] and 
                recent_rsi.iloc[-1] > recent_rsi.iloc[0] and 
                state.rsi < 40):
                signal_type = SignalType.BUY
                stop_loss = current_price * 0.97
                take_profit = current_price * 1.06
            
            # Bearish divergence: Price higher high, RSI lower high
            elif (recent_prices.iloc[-1] > recent_prices.iloc[0] and 
                  recent_rsi.iloc[-1] < recent_rsi.iloc[0] and 
                  state.rsi > 60):
                signal_type = SignalType.SELL
                stop_loss = current_price * 1.03
                take_profit = current_price * 0.94
            
            if not signal_type:
                return None
            
            # Calculate risk-reward
            risk = abs(current_price - stop_loss)
            reward = abs(take_profit - current_price)
            risk_reward_ratio = reward / risk if risk > 0 else 0
            
            if risk_reward_ratio < self.strategy.min_risk_reward:
                return None
            
            return TradingSignal(
                symbol=state.symbol,
                signal_type=signal_type,
                strength=SignalStrength.MODERATE,
                confidence=confidence,
                entry_price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                risk_reward_ratio=risk_reward_ratio,
                position_size_percentage=self._calculate_position_size(risk_reward_ratio, confidence),
                timestamp=datetime.now(),
                timeframe=state.timeframe,
                strategy_name=self.strategy.name,
                reasoning={
                    "pattern": "RSI Divergence",
                    "description": f"RSI divergence detected with {signal_type.value.lower()} bias",
                    "rsi": state.rsi,
                    "rsi_trend": state.rsi_trend,
                    "divergence_type": "bullish" if signal_type == SignalType.BUY else "bearish"
                },
                rsi=state.rsi,
                volume_ratio=state.volume_profile.volume_ratio
            )
            
        except Exception as e:
            print(f"Error checking RSI divergence signal: {e}")
            return None
    
    def _calculate_position_size(self, risk_reward_ratio: float, confidence: float) -> float:
        """Calculate position size based on risk-reward and confidence"""
        try:
            # Base position size
            base_size = self.strategy.max_risk_per_trade
            
            # Adjust based on confidence
            confidence_multiplier = 0.5 + (confidence * 0.5)  # 0.5 to 1.0
            
            # Adjust based on risk-reward ratio
            rr_multiplier = min(risk_reward_ratio / 2.0, 1.5)  # Cap at 1.5x
            
            position_size = base_size * confidence_multiplier * rr_multiplier
            
            # Cap at maximum risk
            return min(position_size, self.strategy.max_risk_per_trade * 1.5)
            
        except Exception as e:
            print(f"Error calculating position size: {e}")
            return self.strategy.max_risk_per_trade
