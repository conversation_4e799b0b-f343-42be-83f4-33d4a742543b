from data.screener_scraper import ScreenerScraper
from data.strike_api import StrikeAP<PERSON>

scraper = ScreenerScraper()
strike_api = StrikeAPI()

# Example 1: Get data for specific date range
symbol = "ETERNAL"
from_date = "2024-01-11T09:15:59+05:30"
to_date = "2024-12-31T23:59:59+05:30"

price_data = strike_api.get_price_data(symbol, from_date, to_date)

if price_data:
    print(f"Fetched {len(price_data)} price records for {symbol}")

# Example 2: Get last 90 days data (simplified)
recent_data = strike_api.get_price_data_simple("RELIANCE", days_back=90)

if recent_data:
    print(f"Fetched {len(recent_data)} recent records for RELIANCE")


stock_data = scraper.scrape_stock_data(symbol)

if stock_data:
    # The data is now a Pydantic model (StockDataResponse)
    print(
        f"✅ Successfully scraped data for {stock_data.company_info.company_name}")
    print(f"📊 Current Price: ₹{stock_data.key_metrics.current_price}")
    print(f"🏢 Sector: {stock_data.company_info.sector}")
    print(f"📈 Market Cap: ₹{stock_data.key_metrics.market_cap_in_cr} Cr")
