import requests
from bs4 import BeautifulSoup
import json
import os
from datetime import datetime
import re
from urllib.parse import urljoin
from .models.models import (
    StockDataResponse, CompanyInfo, KeyMetrics, Financials,
    FinancialRecord, BalanceSheetRecord, CashFlowRecord,
    ShareholdingPattern, ShareholdingRecord, Concall
)

class ScreenerScraper:
    def __init__(self, cache_dir="screener_cache"):
        self.base_url = "https://www.screener.in/company/"
        self.cache_dir = cache_dir

        # Create cache directory if it doesn't exist
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)

    def get_cached_json_data(self, symbol):
        """Get cached JSON data if available"""
        cache_file = os.path.join(self.cache_dir, f"{symbol}.json")

        # Check if cached file exists
        if os.path.exists(cache_file):
            print(f"📁 Using cached JSON data for {symbol}")
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cached_data = json.load(f)
                return StockDataResponse(**cached_data)
            except Exception as e:
                print(f"⚠️ Error reading cached data for {symbol}: {e}")
                # If cache is corrupted, remove it and continue with fresh scraping
                os.remove(cache_file)

        return None

    def save_json_cache(self, symbol, data):
        """Save extracted data to JSON cache"""
        cache_file = os.path.join(self.cache_dir, f"{symbol}.json")
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(data.model_dump(), f, indent=2, ensure_ascii=False, default=str)
            print(f"💾 Cached JSON data for {symbol}")
        except Exception as e:
            print(f"⚠️ Error saving cache for {symbol}: {e}")
    
    def get_html_content(self, symbol):
        """Fetch HTML content from web (no HTML caching, only JSON caching)"""
        print(f"🌐 Fetching fresh HTML for {symbol} from web")
        try:
            url = f"{self.base_url}{symbol}/consolidated/"
            headers = {
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'en-US,en;q=0.9,la;q=0.8',
                'priority': 'u=0, i',
                'referer': 'https://www.screener.in/',
                'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"macOS"',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'same-origin',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1',
                'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            return response.text

        except requests.RequestException as e:
            print(f"❌ Error fetching data for {symbol}: {e}")
            return None
    
    def clean_numeric_value(self, value_str):
        """Clean and convert string to numeric value"""
        if not value_str or value_str.strip() in ['-', '', 'N/A']:
            return None
        
        # Remove commas, spaces, and other non-numeric characters except decimal point and minus
        cleaned = re.sub(r'[^\d.-]', '', str(value_str).replace(',', ''))
        
        try:
            return float(cleaned) if '.' in cleaned else int(cleaned)
        except ValueError:
            return None
    
    def format_date_to_last_day(self, date_str):
        """Convert 'Mar 2021' format to '2021-03-31' (last day of month)"""
        if not date_str:
            return None
        
        # Handle TTM (Trailing Twelve Months)
        if 'TTM' in date_str.upper():
            current_date = datetime.now()
            return current_date.replace(month=12, day=31).strftime('%Y-%m-%d')
        
        # Month mapping
        months = {
            'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
            'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
        }
        
        # Days in month (considering leap year)
        def days_in_month(month, year):
            if month == 2:
                return 29 if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0) else 28
            elif month in [4, 6, 9, 11]:
                return 30
            else:
                return 31
        
        try:
            parts = date_str.strip().split()
            if len(parts) >= 2:
                month_str = parts[0][:3]  # First 3 characters
                year = int(parts[1])
                
                if month_str in months:
                    month = months[month_str]
                    day = days_in_month(month, year)
                    return f"{year}-{month:02d}-{day:02d}"
        except:
            pass
        
        return None
    
    def extract_company_info(self, soup, symbol):
        """Extract company information optimized way"""
        company_info = {
            "symbol": symbol,
            "company_name": "",
            "about": "",
            "sector": ""
        }
        
        # Get company name from h1
        h1_tag = soup.find('h1')
        if h1_tag:
            company_info["company_name"] = h1_tag.get_text(strip=True)
        
        # Get about section - optimized approach
        about_div = soup.find('div', class_='about')
        if about_div:
            p_tags = about_div.find_all('p')
            about_text = ' '.join([p.get_text(strip=True) for p in p_tags])
            company_info["about"] = about_text
        
        # Extract sector from peer comparison section - look for icon-industry
        try:
            # Find the icon-industry element
            industry_icon = soup.find('i', class_='icon-industry')
            if industry_icon:
                next_sibling = industry_icon.find_next('a')
                if next_sibling:
                    company_info["sector"] = next_sibling.get_text(strip=True)
        except:
            pass
        
        return company_info
    
    def extract_key_metrics(self, soup):
        """Extract key financial metrics"""
        key_metrics = {
            "market_cap_in_cr": None,
            "stock_pe": None,
            "current_price": None,
            "high": None,
            "low": None,
            "book_value": None,
            "face_value": None,
            "dividend_yield_percentage": None,
            "roe_percentage": None,
            "roce_percentage": None
        }
        
        # Extract from various sections
        try:
            # Current price from price display
            price_element = soup.find('span', class_='number')
            if price_element:
                key_metrics["current_price"] = self.clean_numeric_value(price_element.get_text())
            
            # Extract from top ratios section
            ratio_elements = soup.find_all('li', class_='flex flex-space-between')
            for element in ratio_elements:
                name_span = element.find('span', class_='name')
                
                if name_span:
                    name = name_span.get_text(strip=True).lower()
                    
                    # Handle High/Low specifically
                    if 'high' in name and 'low' in name:
                        # Find the value span that contains two number spans
                        value_span = element.find('span', class_='value')
                        if value_span:
                            number_spans = value_span.find_all('span', class_='number')
                            if len(number_spans) >= 2:
                                # First number is high, second is low
                                key_metrics["high"] = self.clean_numeric_value(number_spans[0].get_text())
                                key_metrics["low"] = self.clean_numeric_value(number_spans[1].get_text())
                                print(f"✅ Found High: {key_metrics['high']}, Low: {key_metrics['low']}")
                    else:
                        # Handle other metrics with single number span
                        number_span = element.find('span', class_='number')
                        if number_span:
                            value = self.clean_numeric_value(number_span.get_text())
                            
                            if 'market cap' in name:
                                key_metrics["market_cap_in_cr"] = value
                            elif 'stock p/e' in name or name == 'pe':
                                key_metrics["stock_pe"] = value
                            elif 'book value' in name:
                                key_metrics["book_value"] = value
                            elif 'face value' in name:
                                key_metrics["face_value"] = value
                            elif 'dividend yield' in name:
                                key_metrics["dividend_yield_percentage"] = value
                            elif 'roe' in name and 'roce' not in name:
                                key_metrics["roe_percentage"] = value
                            elif 'roce' in name:
                                key_metrics["roce_percentage"] = value
                            elif 'current price' in name:
                                key_metrics["current_price"] = value
                                
        except Exception as e:
            print(f"Warning: Error extracting key metrics: {e}")
        
        return key_metrics

    
    def extract_financial_data(self, soup):
        """Extract financial data based on section IDs"""
        financials = {
            "quarterly_results": [],
            "annual_results": [],
            "balance_sheets": [],
            "cash_flows": []
        }
        
        try:
            # Extract quarterly results from "quarters" section
            quarters_section = soup.find('section', id='quarters')
            if quarters_section:
                table = quarters_section.find('table')
                if table:
                    self.process_profit_loss_table(table, financials["quarterly_results"])
            
            # Extract annual results from "profit-loss" section
            profit_loss_section = soup.find('section', id='profit-loss')
            if profit_loss_section:
                table = profit_loss_section.find('table')
                if table:
                    self.process_profit_loss_table(table, financials["annual_results"], is_annual=True)
            
            # Extract balance sheets
            balance_sheet_section = soup.find('section', id='balance-sheet')
            if balance_sheet_section:
                table = balance_sheet_section.find('table')
                if table:
                    self.process_balance_sheet_table(table, financials["balance_sheets"])
            
            # Extract cash flows
            cash_flow_section = soup.find('section', id='cash-flow')
            if cash_flow_section:
                table = cash_flow_section.find('table')
                if table:
                    self.process_cash_flow_table(table, financials["cash_flows"])
                    
        except Exception as e:
            print(f"Warning: Error extracting financial data: {e}")
        
        return financials
    
    def process_profit_loss_table(self, table, target_list, is_annual=False):
        """Process profit & loss table data"""
        try:
            # Get headers
            headers = []
            header_row = table.find('thead')
            if header_row:
                for th in header_row.find_all(['th', 'td']):
                    headers.append(th.get_text(strip=True))
            
            # Get data rows
            rows = table.find('tbody')
            if not rows:
                rows = table
            
            data_rows = rows.find_all('tr')[1:] if not table.find('thead') else rows.find_all('tr')
            
            for row in data_rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 2:
                    continue
                
                row_name = cells[0].get_text(strip=True).lower()
                
                # Skip header rows
                if any(keyword in row_name for keyword in ['period', 'year', 'quarter']):
                    continue
                
                # Process each period column
                for i, cell in enumerate(cells[1:], 1):
                    if i >= len(headers):
                        continue
                    
                    period_header = headers[i]
                    period_date = self.format_date_to_last_day(period_header)
                    
                    if not period_date:
                        continue
                    
                    value = self.clean_numeric_value(cell.get_text())
                    
                    # Find existing record for this period
                    existing_record = next((r for r in target_list if r["period"] == period_date), None)
                    
                    if not existing_record:
                        existing_record = {"period": period_date}
                        target_list.append(existing_record)
                    
                    # Map row names to JSON keys
                    if 'sales' in row_name or 'revenue' in row_name:
                        existing_record["sales_in_cr"] = value
                    elif 'expenses' in row_name:
                        existing_record["expenses_in_cr"] = value
                    elif 'operating profit' in row_name:
                        existing_record["operating_profit_in_cr"] = value
                    elif 'other income' in row_name:
                        existing_record["other_income_in_cr"] = value
                    elif 'interest' in row_name:
                        existing_record["interest_in_cr"] = value
                    elif 'depreciation' in row_name:
                        existing_record["depreciation_in_cr"] = value
                    elif 'profit before tax' in row_name:
                        existing_record["profit_before_tax_in_cr"] = value
                    elif 'tax' in row_name and 'after' not in row_name:
                        existing_record["tax_in_cr"] = value
                    elif 'net profit' in row_name:
                        existing_record["net_profit_in_cr"] = value
                    elif 'eps' in row_name:
                        existing_record["eps"] = value
                    elif is_annual and 'dividend' in row_name and 'payout' in row_name:
                        existing_record["dividend_payout_percentage"] = value
        except Exception as e:
            print(f"Warning: Error processing profit/loss table: {e}")
    
    def process_balance_sheet_table(self, table, target_list):
        """Process balance sheet table data"""
        try:
            # Get headers
            headers = []
            header_row = table.find('thead')
            if header_row:
                for th in header_row.find_all(['th', 'td']):
                    headers.append(th.get_text(strip=True))
            
            # Get data rows
            rows = table.find('tbody')
            if not rows:
                rows = table
            
            data_rows = rows.find_all('tr')[1:] if not table.find('thead') else rows.find_all('tr')
            
            for row in data_rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 2:
                    continue
                
                row_name = cells[0].get_text(strip=True).lower()
                
                # Process each period column
                for i, cell in enumerate(cells[1:], 1):
                    if i >= len(headers):
                        continue
                    
                    period_header = headers[i]
                    period_date = self.format_date_to_last_day(period_header)
                    
                    if not period_date:
                        continue
                    
                    value = self.clean_numeric_value(cell.get_text())
                    
                    # Find existing record for this period
                    existing_record = next((r for r in target_list if r["period"] == period_date), None)
                    
                    if not existing_record:
                        existing_record = {"period": period_date}
                        target_list.append(existing_record)
                    
                    # Map row names to JSON keys
                    if 'equity' in row_name and 'capital' in row_name:
                        existing_record["equity_capital_in_cr"] = value
                    elif 'reserves' in row_name:
                        existing_record["reserves_in_cr"] = value
                    elif 'borrowings' in row_name:
                        existing_record["borrowings_in_cr"] = value
                    elif 'other liabilities' in row_name:
                        existing_record["other_liabilities_in_cr"] = value
                    elif 'total liabilities' in row_name:
                        existing_record["total_liabilities_in_cr"] = value
                    elif 'fixed assets' in row_name:
                        existing_record["fixed_assets_in_cr"] = value
                    elif 'work in progress' in row_name or 'cwip' in row_name:
                        existing_record["capital_work_in_progress_in_cr"] = value
                    elif 'investments' in row_name:
                        existing_record["investments_in_cr"] = value
                    elif 'other assets' in row_name:
                        existing_record["other_assets_in_cr"] = value
                    elif 'total assets' in row_name:
                        existing_record["total_assets_in_cr"] = value
        except Exception as e:
            print(f"Warning: Error processing balance sheet table: {e}")
    
    def process_cash_flow_table(self, table, target_list):
        """Process cash flow table data"""
        try:
            # Get headers
            headers = []
            header_row = table.find('thead')
            if header_row:
                for th in header_row.find_all(['th', 'td']):
                    headers.append(th.get_text(strip=True))
            
            # Get data rows
            rows = table.find('tbody')
            if not rows:
                rows = table
            
            data_rows = rows.find_all('tr')[1:] if not table.find('thead') else rows.find_all('tr')
            
            for row in data_rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 2:
                    continue
                
                row_name = cells[0].get_text(strip=True).lower()
                
                # Process each period column
                for i, cell in enumerate(cells[1:], 1):
                    if i >= len(headers):
                        continue
                    
                    period_header = headers[i]
                    period_date = self.format_date_to_last_day(period_header)
                    
                    if not period_date:
                        continue
                    
                    value = self.clean_numeric_value(cell.get_text())
                    
                    # Find existing record for this period
                    existing_record = next((r for r in target_list if r["period"] == period_date), None)
                    
                    if not existing_record:
                        existing_record = {"period": period_date}
                        target_list.append(existing_record)
                    
                    # Map row names to JSON keys
                    if 'operating' in row_name and 'activity' in row_name:
                        existing_record["cash_from_operating_activity_in_cr"] = value
                    elif 'investing' in row_name and 'activity' in row_name:
                        existing_record["cash_from_investing_activity_in_cr"] = value
                    elif 'financing' in row_name and 'activity' in row_name:
                        existing_record["cash_from_financing_activity_in_cr"] = value
                    elif 'net cash' in row_name:
                        existing_record["net_cash_flow_in_cr"] = value
        except Exception as e:
            print(f"Warning: Error processing cash flow table: {e}")
    
    def extract_shareholding_pattern(self, soup):
        """Extract shareholding pattern from separate quarterly and yearly divs"""
        shareholding = {
            "quarterly_data": [],
            "annual_data": []
        }
        
        try:
            # Extract quarterly shareholding pattern
            quarterly_div = soup.find('div', id='quarterly-shp')
            if quarterly_div:
                table = quarterly_div.find('table')
                if table:
                    self.process_shareholding_table(table, shareholding["quarterly_data"])
            
            # Extract yearly shareholding pattern
            yearly_div = soup.find('div', id='yearly-shp')
            if yearly_div:
                table = yearly_div.find('table')
                if table:
                    self.process_shareholding_table(table, shareholding["annual_data"])
                    
        except Exception as e:
            print(f"Warning: Error extracting shareholding pattern: {e}")
        
        return shareholding
    
    def process_shareholding_table(self, table, target_list):
        """Process shareholding table data"""
        try:
            # Get headers
            headers = []
            header_row = table.find('thead')
            if header_row:
                for th in header_row.find_all(['th', 'td']):
                    headers.append(th.get_text(strip=True))
            
            # Get data rows
            rows = table.find('tbody')
            if not rows:
                rows = table
            
            data_rows = rows.find_all('tr')[1:] if not table.find('thead') else rows.find_all('tr')
            
            # Process shareholding categories
            categories = {}
            for row in data_rows:
                cells = row.find_all(['td', 'th'])
                if len(cells) < 2:
                    continue
                
                category = cells[0].get_text(strip=True).lower()
                
                # Map categories to JSON keys
                if 'promoter' in category:
                    key = 'promoters_percentage'
                elif 'fii' in category:
                    key = 'fiis_percentage'
                elif 'dii' in category:
                    key = 'diis_percentage'
                elif 'government' in category:
                    key = 'government_percentage'
                elif 'public' in category:
                    key = 'public_percentage'
                else:
                    continue
                
                categories[key] = cells[1:]
            
            # Process each period
            for i, header in enumerate(headers[1:], 1):
                period_date = self.format_date_to_last_day(header)
                if not period_date:
                    continue
                
                record = {"period": period_date}
                
                for key, cells in categories.items():
                    if i-1 < len(cells):
                        value = self.clean_numeric_value(cells[i-1].get_text())
                        record[key] = value
                
                target_list.append(record)
        except Exception as e:
            print(f"Warning: Error processing shareholding table: {e}")
    
    def extract_concalls(self, soup):
        """Extract concalls data - fixed approach based on provided structure"""
        concalls = []
        
        try:
            # Find div with "concalls" class
            concalls_div = soup.find('div', class_='concalls')
            if not concalls_div:
                print("⚠️ Concalls div not found")
                return concalls
            
            print("✅ Found concalls div")
            
            # Find the second div (show-more-box)
            show_more_div = concalls_div.find('div', class_='show-more-box')
            if not show_more_div:
                print("⚠️ Show-more-box div not found")
                return concalls
            
            print("✅ Found show-more-box div")
            
            # Find the unordered list
            ul_element = show_more_div.find('ul')
            if not ul_element:
                print("⚠️ UL element not found")
                return concalls
            
            print("✅ Found UL element")
            
            # Get all li elements (top 3)
            li_elements = ul_element.find_all('li')[:3]
            print(f"📋 Found {len(li_elements)} concall items")
            
            for i, li in enumerate(li_elements):
                try:
                    # Extract period from the first div with specific styling
                    period_div = li.find('div', class_='ink-600')
                    period = ""
                    if period_div:
                        period_text = period_div.get_text(strip=True)
                        period = self.format_date_to_last_day(period_text)
                        print(f"  Period {i+1}: {period_text} -> {period}")
                    
                    # Find transcript and PPT URLs
                    transcript_url = ""
                    ppt_url = ""
                    
                    # Get all concall-link elements in this li
                    concall_links = li.find_all(class_='concall-link')
                    
                    for link in concall_links:
                        link_text = link.get_text(strip=True).lower()
                        
                        if 'transcript' in link_text:
                            if link.name == 'a':
                                transcript_url = link.get('href', '')
                                if transcript_url and not transcript_url.startswith('http'):
                                    transcript_url = urljoin("https://www.screener.in", transcript_url)
                                print(f"    Transcript URL: {transcript_url}")
                        elif 'ppt' in link_text:
                            if link.name == 'a':
                                ppt_url = link.get('href', '')
                                if ppt_url and not ppt_url.startswith('http'):
                                    ppt_url = urljoin("https://www.screener.in", ppt_url)
                                print(f"    PPT URL: {ppt_url}")
                    
                    concalls.append({
                        "period": period if period else f"2024-0{3+i}-31",  # fallback period
                        "transcript_url": transcript_url,
                        "ppt_url": ppt_url
                    })
                    
                except Exception as e:
                    print(f"❌ Error processing concall item {i+1}: {e}")
                    continue
            
            print(f"✅ Extracted {len(concalls)} concalls")
            
        except Exception as e:
            print(f"❌ Error extracting concalls: {e}")
        
        return concalls
    
    def scrape_stock_data(self, symbol):
        """Main API service function that returns Pydantic models with JSON caching"""
        print(f"🚀 Starting data extraction for {symbol}")

        # Check for cached JSON data first
        cached_data = self.get_cached_json_data(symbol)
        if cached_data:
            return cached_data

        # Get HTML content
        html_content = self.get_html_content(symbol)
        if not html_content:
            return None

        # Parse HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # Extract all sections
        company_info_dict = self.extract_company_info(soup, symbol)
        key_metrics_dict = self.extract_key_metrics(soup)
        financials_dict = self.extract_financial_data(soup)
        shareholding_pattern_dict = self.extract_shareholding_pattern(soup)
        concalls_list = self.extract_concalls(soup)

        try:
            # Create Pydantic models
            company_info = CompanyInfo(**company_info_dict)
            key_metrics = KeyMetrics(**key_metrics_dict)

            # Create financial records
            quarterly_results = [FinancialRecord(**record) for record in financials_dict["quarterly_results"]]
            annual_results = [FinancialRecord(**record) for record in financials_dict["annual_results"]]
            balance_sheets = [BalanceSheetRecord(**record) for record in financials_dict["balance_sheets"]]
            cash_flows = [CashFlowRecord(**record) for record in financials_dict["cash_flows"]]

            financials = Financials(
                quarterly_results=quarterly_results,
                annual_results=annual_results,
                balance_sheets=balance_sheets,
                cash_flows=cash_flows
            )

            # Create shareholding pattern
            quarterly_shareholding = [ShareholdingRecord(**record) for record in shareholding_pattern_dict["quarterly_data"]]
            annual_shareholding = [ShareholdingRecord(**record) for record in shareholding_pattern_dict["annual_data"]]

            shareholding_pattern = ShareholdingPattern(
                quarterly_data=quarterly_shareholding,
                annual_data=annual_shareholding
            )

            # Create concalls
            concalls = [Concall(**concall) for concall in concalls_list]

            # Create final response
            stock_data = StockDataResponse(
                company_info=company_info,
                key_metrics=key_metrics,
                financials=financials,
                shareholding_pattern=shareholding_pattern,
                concalls=concalls
            )

            # Save to JSON cache
            self.save_json_cache(symbol, stock_data)

            print(f"✅ Data extraction completed for {symbol}")
            return stock_data

        except Exception as e:
            print(f"❌ Error creating Pydantic models for {symbol}: {e}")
            return None
