import requests
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel
from urllib.parse import quote


class PriceTick(BaseModel):
    datetime: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    delivery_volume: int


class StrikePriceData(BaseModel):
    statistic: float
    count: int
    fields: List[str]
    ticks: dict[str, List[List]]  # ticker -> list of price arrays


class StrikePriceResponse(BaseModel):
    data: StrikePriceData


class StrikeAPI:
    def __init__(self):
        self.base_url = "https://api-v2.strike.money/v2/api/equity/priceticks"
        self.headers = {
            'accept': 'application/json',
            'accept-language': 'en-US,en;q=0.9,la;q=0.8',
            'content-type': 'application/json',
            'origin': 'https://web.strike.money',
            'priority': 'u=1, i',
            'referer': 'https://web.strike.money/',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }

    def get_price_data(
        self,
        symbol: str,
        from_date: str,
        to_date: str,
        candle_interval: str = "1d"
    ) -> Optional[List[PriceTick]]:
        """
        Fetch price data from Strike Money API
        
        Args:
            symbol: Stock symbol (e.g., "TATAMOTORS")
            from_date: Start date in ISO format (e.g., "2024-01-11T09:15:59+05:30")
            to_date: End date in ISO format (e.g., "2025-07-25T23:59:59+05:30")
            candle_interval: Candle interval (e.g., "1d", "1h", "5m")
        
        Returns:
            List of PriceTick objects or None if error
        """
        try:
            # URL encode the dates
            from_encoded = quote(from_date)
            to_encoded = quote(to_date)
            securities = f"EQ:{symbol}"
            
            url = f"{self.base_url}?candleInterval={candle_interval}&from={from_encoded}&to={to_encoded}&securities={securities}"
            
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            
            # Parse response
            strike_response = StrikePriceResponse(**response.json())
            
            # Convert to PriceTick objects
            price_ticks = []
            if symbol in strike_response.data.ticks:
                for tick_data in strike_response.data.ticks[symbol]:
                    price_tick = PriceTick(
                        datetime=tick_data[0],
                        open=tick_data[1],
                        high=tick_data[2],
                        low=tick_data[3],
                        close=tick_data[4],
                        volume=tick_data[5],
                        delivery_volume=tick_data[6]
                    )
                    price_ticks.append(price_tick)
            
            return price_ticks
            
        except requests.RequestException as e:
            print(f"Error fetching data from Strike API: {e}")
            return None
        except Exception as e:
            print(f"Error parsing Strike API response: {e}")
            return None

    def get_price_data_simple(
        self,
        symbol: str,
        days_back: int = 365
    ) -> Optional[List[PriceTick]]:
        """
        Simplified method to get price data for last N days
        
        Args:
            symbol: Stock symbol
            days_back: Number of days to go back from today
        
        Returns:
            List of PriceTick objects or None if error
        """
        from datetime import timedelta
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        # Format dates for Strike API
        from_date = start_date.strftime("%Y-%m-%dT09:15:59+05:30")
        to_date = end_date.strftime("%Y-%m-%dT23:59:59+05:30")
        
        return self.get_price_data(symbol, from_date, to_date)