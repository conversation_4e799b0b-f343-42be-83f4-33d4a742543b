"""
LLM model configuration and management for Ollama
"""
import os
import json
from pathlib import Path
from typing import List, Optional
from pydantic import BaseModel
from langchain_ollama import ChatOllama
from enum import Enum


class ModelProvider(str, Enum):
    """Enum for supported LLM providers"""
    OLLAMA = "Ollama"


class LLMModel(BaseModel):
    """Represents an LLM model configuration"""
    display_name: str
    model_name: str
    provider: ModelProvider

    def is_custom(self) -> bool:
        """Check if the model is a custom model"""
        return self.model_name == "-"

    def has_json_mode(self) -> bool:
        """Check if the model supports JSON mode"""
        # Most Ollama models support JSON mode
        return "llama" in self.model_name.lower() or "mistral" in self.model_name.lower()


def load_ollama_models() -> List[LLMModel]:
    """Load Ollama models from JSON configuration"""
    current_dir = Path(__file__).parent
    models_json_path = current_dir / "ollama_models.json"
    
    try:
        with open(models_json_path, 'r') as f:
            models_data = json.load(f)
        
        models = []
        for model_data in models_data:
            models.append(
                LLMModel(
                    display_name=model_data["display_name"],
                    model_name=model_data["model_name"],
                    provider=ModelProvider.OLLAMA
                )
            )
        return models
    except Exception as e:
        print(f"Error loading Ollama models: {e}")
        # Return default models if file not found
        return [
            LLMModel(
                display_name="Llama 3.2 Latest",
                model_name="llama3.2:latest",
                provider=ModelProvider.OLLAMA
            )
        ]


def get_ollama_model(model_name: str) -> ChatOllama:
    """Get Ollama model instance"""
    # Check if OLLAMA_HOST is set (for Docker on macOS)
    ollama_host = os.getenv("OLLAMA_HOST", "localhost")
    base_url = os.getenv("OLLAMA_BASE_URL", f"http://{ollama_host}:11434")
    
    return ChatOllama(
        model=model_name,
        base_url=base_url,
        temperature=0.1,  # Lower temperature for more consistent analysis
    )


def get_default_model() -> ChatOllama:
    """Get default model for analysis"""
    default_model_name = os.getenv("DEFAULT_MODEL", "llama3.1:latest")
    return get_ollama_model(default_model_name)


def check_ollama_connection() -> bool:
    """Check if Ollama is running and accessible"""
    try:
        model = get_default_model()
        # Try a simple test
        response = model.invoke("Hello")
        return True
    except Exception as e:
        print(f"Ollama connection failed: {e}")
        return False


# Load available models
AVAILABLE_OLLAMA_MODELS = load_ollama_models()
