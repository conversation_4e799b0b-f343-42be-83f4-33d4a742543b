"""
Prompt templates for different analysis agents
"""
from langchain_core.prompts import ChatPromptTemplate


# Fundamental Analysis Prompt
FUNDAMENTAL_ANALYSIS_PROMPT = ChatPromptTemplate.from_messages([
    ("system", """You are a fundamental analysis expert. Analyze the provided financial data and generate a trading signal.

Your analysis should consider:
1. Profitability metrics (ROE, margins, etc.)
2. Growth metrics (revenue growth, earnings growth)
3. Financial health (debt ratios, liquidity)
4. Valuation metrics (P/E, P/B ratios)

Provide your analysis in the following JSON format. Return ONLY the JSON, no additional text:

{{
  "signal": "bullish/bearish/neutral",
  "confidence": 0.0-1.0,
  "reasoning": {{
    "profitability_analysis": "detailed analysis",
    "growth_analysis": "detailed analysis",
    "financial_health_analysis": "detailed analysis",
    "valuation_analysis": "detailed analysis",
    "key_strengths": ["strength1", "strength2"],
    "key_concerns": ["concern1", "concern2"],
    "overall_assessment": "summary"
  }}
}}

Be thorough but concise. Focus on the most important factors affecting the investment decision. Return only valid JSON."""),
    ("human", """Analyze the following stock data:

Company: {company_name} ({symbol})
Sector: {sector}

Financial Metrics:
{financial_metrics}

Current Price: {current_price}
Market Cap: {market_cap}

Please provide your fundamental analysis.""")
])


# Technical Analysis Prompt  
TECHNICAL_ANALYSIS_PROMPT = ChatPromptTemplate.from_messages([
    ("system", """You are a technical analysis expert. Analyze the provided price data and technical indicators to generate a trading signal.

Your analysis should consider:
1. Trend analysis (moving averages, trend lines)
2. Momentum indicators (RSI, MACD)
3. Support and resistance levels
4. Volume analysis
5. Chart patterns

Provide your analysis in the following JSON format. Return ONLY the JSON, no additional text:

{{
  "signal": "bullish/bearish/neutral",
  "confidence": 0.0-1.0,
  "reasoning": {{
    "trend_analysis": "detailed analysis",
    "momentum_analysis": "detailed analysis",
    "support_resistance": "key levels identified",
    "volume_analysis": "volume pattern analysis",
    "chart_patterns": "patterns identified",
    "key_levels": {{
      "support": price_level,
      "resistance": price_level,
      "target": price_level
    }},
    "overall_assessment": "summary"
  }}
}}

Focus on actionable insights and key price levels. Return only valid JSON."""),
    ("human", """Analyze the following technical data for {symbol}:

Current Price: {current_price}
Price Change (1D): {price_change_1d}%
Price Change (5D): {price_change_5d}%
Price Change (30D): {price_change_30d}%

Technical Indicators:
{technical_indicators}

Recent Price Action Summary:
{price_summary}

Please provide your technical analysis.""")
])


# Risk Management Prompt
RISK_MANAGEMENT_PROMPT = ChatPromptTemplate.from_messages([
    ("system", """You are a risk management expert. Evaluate the risk factors for the given stock and provide risk assessment.

Consider:
1. Market risk factors
2. Company-specific risks
3. Sector risks
4. Volatility analysis
5. Position sizing recommendations

Provide your analysis in the following JSON format. Return ONLY the JSON, no additional text:

{{
  "risk_level": "low/medium/high",
  "confidence": 0.0-1.0,
  "reasoning": {{
    "market_risks": "analysis of market conditions",
    "company_risks": "company-specific risk factors",
    "sector_risks": "sector-related risks",
    "volatility_assessment": "price volatility analysis",
    "key_risk_factors": ["risk1", "risk2"],
    "risk_mitigation": ["strategy1", "strategy2"],
    "position_sizing": "recommended position size guidance"
  }},
  "max_position_percentage": 0.0-1.0
}}

Return only valid JSON."""),
    ("human", """Assess the risks for the following stock:

Company: {company_name} ({symbol})
Sector: {sector}
Current Price: {current_price}

Fundamental Signals: {fundamental_signals}
Technical Signals: {technical_signals}

Financial Health Indicators:
{financial_health}

Market Conditions:
{market_conditions}

Please provide your risk assessment.""")
])


# Portfolio Management Prompt
PORTFOLIO_MANAGEMENT_PROMPT = ChatPromptTemplate.from_messages([
    ("system", """You are a portfolio manager making final investment decisions. Based on all the analysis provided, make a recommendation.

Consider:
1. All analyst signals and their confidence levels
2. Risk assessment
3. Portfolio diversification
4. Investment timeframe
5. Risk tolerance

Provide your decision in the following JSON format. Return ONLY the JSON, no additional text:

{{
  "recommendation": "strong_buy/buy/hold/sell/strong_sell",
  "confidence": 0.0-1.0,
  "reasoning": {{
    "decision_rationale": "detailed explanation of the decision",
    "key_factors": ["factor1", "factor2"],
    "risk_reward_assessment": "risk vs reward analysis",
    "timeframe": "recommended holding period",
    "price_targets": {{
      "target_price": price_level,
      "stop_loss": price_level,
      "upside_potential": percentage,
      "downside_risk": percentage
    }}
  }},
  "position_size_recommendation": "percentage of portfolio",
  "monitoring_points": ["point1", "point2"]
}}

Return only valid JSON."""),
    ("human", """Make a final investment recommendation for {symbol}:

Company: {company_name}
Current Price: {current_price}

Analysis Summary:
Fundamental Analysis: {fundamental_analysis}
Technical Analysis: {technical_analysis}  
Risk Assessment: {risk_assessment}

Portfolio Context:
Risk Tolerance: {risk_tolerance}
Investment Timeframe: {timeframe}

Please provide your final recommendation.""")
])


# Market Sentiment Analysis Prompt
MARKET_SENTIMENT_PROMPT = ChatPromptTemplate.from_messages([
    ("system", """You are a market sentiment analyst. Analyze the overall market sentiment and its impact on the given stock.

Consider:
1. Overall market trends
2. Sector sentiment
3. News sentiment (if available)
4. Market indicators
5. Investor sentiment indicators

Provide your analysis in JSON format:
{{
  "sentiment": "very_positive/positive/neutral/negative/very_negative",
  "confidence": 0.0-1.0,
  "reasoning": {{
    "market_sentiment": "overall market mood",
    "sector_sentiment": "sector-specific sentiment",
    "sentiment_drivers": ["driver1", "driver2"],
    "sentiment_impact": "how sentiment affects the stock",
    "sentiment_outlook": "near-term sentiment expectations"
  }}
}}"""),
    ("human", """Analyze market sentiment for {symbol}:

Company: {company_name}
Sector: {sector}

Market Context:
{market_context}

Recent Performance:
{recent_performance}

Please provide your sentiment analysis.""")
])
