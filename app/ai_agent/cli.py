"""
Command Line Interface for AI Stock Analysis Agent
"""
import argparse
import sys
import os
from typing import List
from colorama import Fore, Style, init

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from app.ai_agent.workflow.graph import run_stock_analysis, run_batch_analysis
from app.ai_agent.utils.display import (
    print_analysis_summary, 
    print_batch_summary, 
    export_results_to_json,
    print_header
)
from app.ai_agent.llm.models import check_ollama_connection

# Initialize colorama
init(autoreset=True)


def check_prerequisites():
    """Check if all prerequisites are met"""
    print("🔍 Checking prerequisites...")
    
    # Check Ollama connection
    if not check_ollama_connection():
        print(f"{Fore.RED}❌ Ollama is not running or not accessible{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}Please ensure Ollama is installed and running:{Style.RESET_ALL}")
        print("  1. Install Ollama from https://ollama.ai")
        print("  2. Run: ollama pull llama3.2:latest")
        print("  3. Start Ollama service")
        return False
    
    print(f"{Fore.GREEN}✅ Ollama connection successful{Style.RESET_ALL}")
    return True


def analyze_single_stock(symbol: str, risk_tolerance: str, timeframe: str, export: bool = False):
    """Analyze a single stock"""
    print_header(f"AI STOCK ANALYSIS - {symbol.upper()}")
    
    # Run analysis
    result = run_stock_analysis(symbol, risk_tolerance, timeframe)
    
    # Display results
    print_analysis_summary(result)
    
    # Export if requested
    if export:
        export_results_to_json({symbol: result}, f"{symbol.lower()}_analysis.json")
    
    return result


def analyze_multiple_stocks(symbols: List[str], risk_tolerance: str, timeframe: str, export: bool = False):
    """Analyze multiple stocks"""
    print_header("BATCH STOCK ANALYSIS")
    
    # Run batch analysis
    results = run_batch_analysis(symbols, risk_tolerance, timeframe)
    
    # Display summary
    print_batch_summary(results)
    
    # Show individual results if requested
    show_details = input(f"\n{Fore.CYAN}Show detailed results for each stock? (y/n): {Style.RESET_ALL}").lower() == 'y'
    
    if show_details:
        for symbol, result in results.items():
            if result.final_recommendation:
                print_analysis_summary(result)
            else:
                print(f"\n{Fore.RED}❌ Analysis failed for {symbol}{Style.RESET_ALL}")
                if result.errors:
                    for error in result.errors:
                        print(f"  Error: {error}")
    
    # Export if requested
    if export:
        export_results_to_json(results, "batch_analysis_results.json")
    
    return results


def interactive_mode():
    """Run in interactive mode"""
    print_header("AI STOCK ANALYSIS - INTERACTIVE MODE")
    
    while True:
        print(f"\n{Fore.CYAN}Options:{Style.RESET_ALL}")
        print("1. Analyze single stock")
        print("2. Analyze multiple stocks")
        print("3. Exit")
        
        choice = input(f"\n{Fore.YELLOW}Enter your choice (1-3): {Style.RESET_ALL}")
        
        if choice == "1":
            symbol = input(f"{Fore.CYAN}Enter stock symbol: {Style.RESET_ALL}").strip().upper()
            if not symbol:
                print(f"{Fore.RED}❌ Please enter a valid symbol{Style.RESET_ALL}")
                continue
            
            risk_tolerance = input(f"{Fore.CYAN}Risk tolerance (low/medium/high) [medium]: {Style.RESET_ALL}").strip().lower() or "medium"
            timeframe = input(f"{Fore.CYAN}Investment timeframe (short_term/medium_term/long_term) [medium_term]: {Style.RESET_ALL}").strip().lower() or "medium_term"
            export = input(f"{Fore.CYAN}Export results to JSON? (y/n) [n]: {Style.RESET_ALL}").strip().lower() == 'y'
            
            analyze_single_stock(symbol, risk_tolerance, timeframe, export)
        
        elif choice == "2":
            symbols_input = input(f"{Fore.CYAN}Enter stock symbols (comma-separated): {Style.RESET_ALL}").strip()
            if not symbols_input:
                print(f"{Fore.RED}❌ Please enter valid symbols{Style.RESET_ALL}")
                continue
            
            symbols = [s.strip().upper() for s in symbols_input.split(",")]
            risk_tolerance = input(f"{Fore.CYAN}Risk tolerance (low/medium/high) [medium]: {Style.RESET_ALL}").strip().lower() or "medium"
            timeframe = input(f"{Fore.CYAN}Investment timeframe (short_term/medium_term/long_term) [medium_term]: {Style.RESET_ALL}").strip().lower() or "medium_term"
            export = input(f"{Fore.CYAN}Export results to JSON? (y/n) [n]: {Style.RESET_ALL}").strip().lower() == 'y'
            
            analyze_multiple_stocks(symbols, risk_tolerance, timeframe, export)
        
        elif choice == "3":
            print(f"{Fore.GREEN}👋 Thank you for using AI Stock Analysis Agent!{Style.RESET_ALL}")
            break
        
        else:
            print(f"{Fore.RED}❌ Invalid choice. Please enter 1, 2, or 3.{Style.RESET_ALL}")


def main():
    """Main CLI function"""
    parser = argparse.ArgumentParser(
        description="AI Stock Analysis Agent - Comprehensive stock analysis using AI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m app.ai_agent.cli TATAMOTORS
  python -m app.ai_agent.cli TATAMOTORS RELIANCE --risk-tolerance high
  python -m app.ai_agent.cli --interactive
  python -m app.ai_agent.cli INFY --export --timeframe long_term
        """
    )
    
    parser.add_argument(
        "symbols",
        nargs="*",
        help="Stock symbols to analyze (e.g., TATAMOTORS RELIANCE)"
    )
    
    parser.add_argument(
        "--risk-tolerance",
        choices=["low", "medium", "high"],
        default="medium",
        help="Risk tolerance level (default: medium)"
    )
    
    parser.add_argument(
        "--timeframe",
        choices=["short_term", "medium_term", "long_term"],
        default="medium_term",
        help="Investment timeframe (default: medium_term)"
    )
    
    parser.add_argument(
        "--export",
        action="store_true",
        help="Export results to JSON file"
    )
    
    parser.add_argument(
        "--interactive",
        action="store_true",
        help="Run in interactive mode"
    )
    
    parser.add_argument(
        "--skip-checks",
        action="store_true",
        help="Skip prerequisite checks"
    )
    
    args = parser.parse_args()
    
    # Check prerequisites unless skipped
    if not args.skip_checks:
        if not check_prerequisites():
            sys.exit(1)
    
    # Interactive mode
    if args.interactive:
        interactive_mode()
        return
    
    # Validate symbols
    if not args.symbols:
        print(f"{Fore.RED}❌ Please provide stock symbols or use --interactive mode{Style.RESET_ALL}")
        parser.print_help()
        sys.exit(1)
    
    # Analyze stocks
    try:
        if len(args.symbols) == 1:
            analyze_single_stock(args.symbols[0], args.risk_tolerance, args.timeframe, args.export)
        else:
            analyze_multiple_stocks(args.symbols, args.risk_tolerance, args.timeframe, args.export)
    
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}⚠️ Analysis interrupted by user{Style.RESET_ALL}")
        sys.exit(0)
    except Exception as e:
        print(f"\n{Fore.RED}❌ Unexpected error: {e}{Style.RESET_ALL}")
        sys.exit(1)


if __name__ == "__main__":
    main()
