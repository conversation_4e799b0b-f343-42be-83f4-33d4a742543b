"""
Display utilities for formatting analysis results
"""
from typing import Dict, Any, List
from colorama import Fore, Style, init
import json

# Initialize colorama
init(autoreset=True)


def print_header(title: str):
    """Print a formatted header"""
    print(f"\n{Fore.CYAN}{'=' * 60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{title.center(60)}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'=' * 60}{Style.RESET_ALL}")


def print_section(title: str):
    """Print a section header"""
    print(f"\n{Fore.YELLOW}{title}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}{'-' * len(title)}{Style.RESET_ALL}")


def print_signal(signal: str, confidence: float):
    """Print a colored signal with confidence"""
    if signal.lower() in ["bullish", "buy", "strong_buy"]:
        color = Fore.GREEN
        emoji = "📈"
    elif signal.lower() in ["bearish", "sell", "strong_sell"]:
        color = Fore.RED
        emoji = "📉"
    else:
        color = Fore.YELLOW
        emoji = "➡️"
    
    print(f"{emoji} {color}{signal.upper()}{Style.RESET_ALL} (Confidence: {confidence:.2f})")


def print_analysis_result(analysis_name: str, result):
    """Print analysis result in a formatted way"""
    print_section(f"{analysis_name} Analysis")
    
    if result:
        print_signal(result.signal, result.confidence)
        
        if hasattr(result, 'reasoning') and result.reasoning:
            print(f"\n{Fore.CYAN}Key Insights:{Style.RESET_ALL}")
            
            # Extract and display key reasoning points
            reasoning = result.reasoning
            if isinstance(reasoning, dict):
                for key, value in reasoning.items():
                    if isinstance(value, str) and len(value) < 200:
                        print(f"  • {key.replace('_', ' ').title()}: {value}")
                    elif isinstance(value, list) and len(value) <= 5:
                        print(f"  • {key.replace('_', ' ').title()}: {', '.join(map(str, value))}")
                    elif isinstance(value, dict) and len(str(value)) < 200:
                        print(f"  • {key.replace('_', ' ').title()}: {value}")
    else:
        print(f"{Fore.RED}❌ Analysis not available{Style.RESET_ALL}")


def print_company_info(stock_data):
    """Print company information"""
    if not stock_data or not stock_data.company_data:
        print(f"{Fore.RED}❌ Company information not available{Style.RESET_ALL}")
        return
    
    company = stock_data.company_data
    
    print_section("Company Information")
    print(f"Company: {Fore.CYAN}{company.company_name}{Style.RESET_ALL}")
    print(f"Symbol: {Fore.CYAN}{company.symbol}{Style.RESET_ALL}")
    print(f"Sector: {Fore.CYAN}{company.sector}{Style.RESET_ALL}")
    
    if company.current_price:
        print(f"Current Price: {Fore.GREEN}₹{company.current_price:.2f}{Style.RESET_ALL}")
    
    if company.market_cap_in_cr:
        print(f"Market Cap: {Fore.GREEN}₹{company.market_cap_in_cr:.2f} Cr{Style.RESET_ALL}")
    
    if company.about and len(company.about) > 0:
        # Truncate long descriptions
        about = company.about[:200] + "..." if len(company.about) > 200 else company.about
        print(f"\nAbout: {about}")


def print_financial_metrics(stock_data):
    """Print key financial metrics"""
    if not stock_data or not stock_data.financial_metrics:
        print(f"{Fore.RED}❌ Financial metrics not available{Style.RESET_ALL}")
        return
    
    metrics = stock_data.financial_metrics
    
    print_section("Key Financial Metrics")
    
    # Profitability metrics
    if metrics.return_on_equity:
        print(f"Return on Equity: {Fore.GREEN}{metrics.return_on_equity:.2%}{Style.RESET_ALL}")
    
    if metrics.net_margin:
        print(f"Net Margin: {Fore.GREEN}{metrics.net_margin:.2%}{Style.RESET_ALL}")
    
    # Growth metrics
    if metrics.revenue_growth:
        color = Fore.GREEN if metrics.revenue_growth > 0 else Fore.RED
        print(f"Revenue Growth: {color}{metrics.revenue_growth:.2%}{Style.RESET_ALL}")
    
    if metrics.earnings_growth:
        color = Fore.GREEN if metrics.earnings_growth > 0 else Fore.RED
        print(f"Earnings Growth: {color}{metrics.earnings_growth:.2%}{Style.RESET_ALL}")
    
    # Valuation metrics
    if metrics.price_to_earnings_ratio:
        print(f"P/E Ratio: {Fore.CYAN}{metrics.price_to_earnings_ratio:.2f}{Style.RESET_ALL}")
    
    if metrics.price_to_book_ratio:
        print(f"P/B Ratio: {Fore.CYAN}{metrics.price_to_book_ratio:.2f}{Style.RESET_ALL}")
    
    # Financial health
    if metrics.debt_to_equity:
        color = Fore.GREEN if metrics.debt_to_equity < 0.5 else Fore.RED if metrics.debt_to_equity > 1.0 else Fore.YELLOW
        print(f"Debt to Equity: {color}{metrics.debt_to_equity:.2f}{Style.RESET_ALL}")


def print_final_recommendation(final_recommendation):
    """Print the final investment recommendation"""
    if not final_recommendation:
        print(f"{Fore.RED}❌ Final recommendation not available{Style.RESET_ALL}")
        return
    
    print_header("FINAL INVESTMENT RECOMMENDATION")
    
    recommendation = final_recommendation.get("recommendation", {})
    action = recommendation.get("action", "hold")
    confidence = recommendation.get("confidence", 0.0)
    
    # Print main recommendation
    print_signal(action, confidence)
    
    # Print risk level
    risk_level = final_recommendation.get("risk_level", "unknown")
    risk_color = Fore.GREEN if risk_level == "low" else Fore.RED if risk_level == "high" else Fore.YELLOW
    print(f"\nRisk Level: {risk_color}{risk_level.upper()}{Style.RESET_ALL}")
    
    # Print analysis quality
    quality = final_recommendation.get("analysis_quality", {})
    success_rate = quality.get("success_rate", 0)
    quality_color = Fore.GREEN if success_rate > 80 else Fore.YELLOW if success_rate > 60 else Fore.RED
    print(f"Analysis Quality: {quality_color}{success_rate:.1f}%{Style.RESET_ALL}")
    
    # Print key reasoning if available
    reasoning = recommendation.get("reasoning", {})
    if reasoning:
        print(f"\n{Fore.CYAN}Key Decision Factors:{Style.RESET_ALL}")
        
        # Extract important reasoning points
        if "decision_rationale" in reasoning:
            print(f"  • {reasoning['decision_rationale']}")
        
        if "key_factors" in reasoning and isinstance(reasoning["key_factors"], list):
            for factor in reasoning["key_factors"][:3]:  # Show top 3 factors
                print(f"  • {factor}")
        
        if "price_targets" in reasoning and isinstance(reasoning["price_targets"], dict):
            targets = reasoning["price_targets"]
            if "target_price" in targets:
                print(f"  • Target Price: ₹{targets['target_price']:.2f}")
            if "stop_loss" in targets:
                print(f"  • Stop Loss: ₹{targets['stop_loss']:.2f}")


def print_analysis_summary(state):
    """Print complete analysis summary"""
    symbol = state["symbol"] if isinstance(state, dict) else state.symbol
    print_header(f"AI STOCK ANALYSIS - {symbol}")

    # Company information
    stock_data = state["stock_data"] if isinstance(state, dict) else state.stock_data
    print_company_info(stock_data)

    # Financial metrics
    print_financial_metrics(stock_data)

    # Analysis results
    fundamental_result = state["fundamental_result"] if isinstance(state, dict) else state.fundamental_analysis
    technical_result = state["technical_result"] if isinstance(state, dict) else state.technical_analysis
    risk_result = state["risk_result"] if isinstance(state, dict) else state.risk_analysis

    print_analysis_result("Fundamental", fundamental_result)
    print_analysis_result("Technical", technical_result)
    print_analysis_result("Risk", risk_result)

    # Final recommendation
    final_recommendation = state["final_recommendation"] if isinstance(state, dict) else state.final_recommendation
    print_final_recommendation(final_recommendation)

    # Errors if any
    errors = state["errors"] if isinstance(state, dict) else state.errors
    if errors:
        print_section("Analysis Warnings")
        for error in errors:
            print(f"{Fore.RED}⚠️ {error}{Style.RESET_ALL}")

    print(f"\n{Fore.CYAN}Analysis completed for {symbol}{Style.RESET_ALL}")


def print_batch_summary(results: Dict[str, Any]):
    """Print summary of batch analysis results"""
    print_header("BATCH ANALYSIS SUMMARY")
    
    total_stocks = len(results)
    successful_analyses = sum(1 for result in results.values() if
                             (result["final_recommendation"] if isinstance(result, dict) else result.final_recommendation))
    
    print(f"Total Stocks Analyzed: {Fore.CYAN}{total_stocks}{Style.RESET_ALL}")
    print(f"Successful Analyses: {Fore.GREEN}{successful_analyses}{Style.RESET_ALL}")
    print(f"Success Rate: {Fore.YELLOW}{(successful_analyses/total_stocks)*100:.1f}%{Style.RESET_ALL}")
    
    print_section("Recommendations Summary")
    
    recommendations = {}
    for symbol, result in results.items():
        final_rec = result["final_recommendation"] if isinstance(result, dict) else result.final_recommendation
        if final_rec:
            action = final_rec["recommendation"]["action"]
            confidence = final_rec["recommendation"]["confidence"]
            recommendations[symbol] = (action, confidence)
    
    # Group by recommendation type
    buy_stocks = [(s, c) for s, (a, c) in recommendations.items() if a in ["buy", "strong_buy"]]
    sell_stocks = [(s, c) for s, (a, c) in recommendations.items() if a in ["sell", "strong_sell"]]
    hold_stocks = [(s, c) for s, (a, c) in recommendations.items() if a == "hold"]
    
    if buy_stocks:
        print(f"\n{Fore.GREEN}📈 BUY RECOMMENDATIONS:{Style.RESET_ALL}")
        for symbol, confidence in sorted(buy_stocks, key=lambda x: x[1], reverse=True):
            print(f"  • {symbol}: {confidence:.2f} confidence")
    
    if sell_stocks:
        print(f"\n{Fore.RED}📉 SELL RECOMMENDATIONS:{Style.RESET_ALL}")
        for symbol, confidence in sorted(sell_stocks, key=lambda x: x[1], reverse=True):
            print(f"  • {symbol}: {confidence:.2f} confidence")
    
    if hold_stocks:
        print(f"\n{Fore.YELLOW}➡️ HOLD RECOMMENDATIONS:{Style.RESET_ALL}")
        for symbol, confidence in sorted(hold_stocks, key=lambda x: x[1], reverse=True):
            print(f"  • {symbol}: {confidence:.2f} confidence")


def export_results_to_json(results: Dict[str, Any], filename: str = "analysis_results.json"):
    """Export analysis results to JSON file"""
    try:
        # Convert results to JSON-serializable format
        json_results = {}
        for symbol, result in results.items():
            final_rec = result["final_recommendation"] if isinstance(result, dict) else getattr(result, 'final_recommendation', None)
            if final_rec:
                json_results[symbol] = final_rec
            else:
                errors = result["errors"] if isinstance(result, dict) else getattr(result, 'errors', [])
                json_results[symbol] = {
                    "symbol": symbol,
                    "error": "Analysis failed",
                    "errors": errors
                }
        
        with open(filename, 'w') as f:
            json.dump(json_results, f, indent=2, default=str)
        
        print(f"\n{Fore.GREEN}✅ Results exported to {filename}{Style.RESET_ALL}")
        
    except Exception as e:
        print(f"\n{Fore.RED}❌ Error exporting results: {e}{Style.RESET_ALL}")
