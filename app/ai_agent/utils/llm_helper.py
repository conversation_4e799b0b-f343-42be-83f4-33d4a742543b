"""
LLM helper utilities for making calls and handling responses
"""
import json
import re
from typing import Any, Dict, Optional, Type, TypeVar
from pydantic import BaseModel, ValidationError
from langchain_core.prompts import ChatPromptTemplate
from app.ai_agent.llm.models import get_default_model

T = TypeVar('T', bound=BaseModel)


def call_llm_with_json_response(
    prompt: ChatPromptTemplate,
    prompt_variables: Dict[str, Any],
    response_model: Optional[Type[T]] = None,
    max_retries: int = 3
) -> Optional[T]:
    """
    Call LLM with prompt and return structured JSON response
    
    Args:
        prompt: ChatPromptTemplate to use
        prompt_variables: Variables to fill in the prompt
        response_model: Pydantic model to validate response
        max_retries: Maximum number of retries on failure
    
    Returns:
        Parsed response model or None if failed
    """
    model = get_default_model()
    
    for attempt in range(max_retries):
        try:
            # Generate the prompt
            formatted_prompt = prompt.invoke(prompt_variables)
            
            # Call the model
            response = model.invoke(formatted_prompt)
            
            # Extract content
            if hasattr(response, 'content'):
                content = response.content
            else:
                content = str(response)
            
            # Try to parse as JSON
            try:
                json_response = json.loads(content)
            except json.JSONDecodeError:
                # Try to extract JSON from the response if it's wrapped in text
                json_response = extract_json_from_text(content)
                if not json_response:
                    print(f"Attempt {attempt + 1}: Failed to parse JSON from response")
                    print(f"Raw response (first 200 chars): {content[:200]}...")
                    continue
            
            # Validate with Pydantic model if provided
            if response_model:
                try:
                    return response_model(**json_response)
                except ValidationError as e:
                    print(f"Attempt {attempt + 1}: Validation error: {e}")
                    continue
            else:
                return json_response
                
        except Exception as e:
            print(f"Attempt {attempt + 1}: LLM call failed: {e}")
            continue
    
    print(f"Failed to get valid response after {max_retries} attempts")
    return None


def extract_json_from_text(text: str) -> Optional[Dict[str, Any]]:
    """
    Extract JSON from text that might contain other content
    """
    if not text or not text.strip():
        return None

    # Try multiple extraction strategies
    strategies = [
        _extract_json_basic,
        _extract_json_with_markdown,
        _extract_json_multiline,
        _extract_json_loose
    ]

    for strategy in strategies:
        try:
            result = strategy(text)
            if result:
                return result
        except Exception:
            continue

    return None


def _extract_json_basic(text: str) -> Optional[Dict[str, Any]]:
    """Basic JSON extraction"""
    start_idx = text.find('{')
    if start_idx == -1:
        return None

    # Find the matching closing brace
    brace_count = 0
    end_idx = start_idx

    for i, char in enumerate(text[start_idx:], start_idx):
        if char == '{':
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                end_idx = i
                break

    if brace_count != 0:
        return None

    json_str = text[start_idx:end_idx + 1]
    return json.loads(json_str)


def _extract_json_with_markdown(text: str) -> Optional[Dict[str, Any]]:
    """Extract JSON from markdown code blocks"""
    import re

    # Look for JSON in markdown code blocks
    patterns = [
        r'```json\s*(\{.*?\})\s*```',
        r'```\s*(\{.*?\})\s*```',
        r'`(\{.*?\})`'
    ]

    for pattern in patterns:
        match = re.search(pattern, text, re.DOTALL)
        if match:
            json_str = match.group(1).strip()
            return json.loads(json_str)

    return None


def _extract_json_multiline(text: str) -> Optional[Dict[str, Any]]:
    """Extract JSON handling multiline responses"""
    lines = text.split('\n')
    json_lines = []
    in_json = False
    brace_count = 0

    for line in lines:
        line = line.strip()
        if not in_json and line.startswith('{'):
            in_json = True
            json_lines.append(line)
            brace_count += line.count('{') - line.count('}')
        elif in_json:
            json_lines.append(line)
            brace_count += line.count('{') - line.count('}')
            if brace_count == 0:
                break

    if json_lines and brace_count == 0:
        json_str = '\n'.join(json_lines)
        return json.loads(json_str)

    return None


def _extract_json_loose(text: str) -> Optional[Dict[str, Any]]:
    """Loose JSON extraction with cleaning"""
    # Remove common prefixes/suffixes
    text = text.strip()

    # Remove common response prefixes
    prefixes_to_remove = [
        "Here's the analysis:",
        "Based on the analysis:",
        "The analysis shows:",
        "Analysis result:",
        "Here is the JSON:",
        "```json",
        "```"
    ]

    for prefix in prefixes_to_remove:
        if text.lower().startswith(prefix.lower()):
            text = text[len(prefix):].strip()

    # Find JSON boundaries more aggressively
    start_idx = -1
    for i, char in enumerate(text):
        if char == '{':
            start_idx = i
            break

    if start_idx == -1:
        return None

    end_idx = -1
    brace_count = 0
    for i in range(start_idx, len(text)):
        if text[i] == '{':
            brace_count += 1
        elif text[i] == '}':
            brace_count -= 1
            if brace_count == 0:
                end_idx = i
                break

    if end_idx == -1:
        return None

    json_str = text[start_idx:end_idx + 1]

    # Clean up common issues
    json_str = json_str.replace('\n', ' ')
    json_str = json_str.replace('\t', ' ')

    # Fix common JSON issues
    json_str = re.sub(r',\s*}', '}', json_str)  # Remove trailing commas
    json_str = re.sub(r',\s*]', ']', json_str)  # Remove trailing commas in arrays

    return json.loads(json_str)


def format_financial_metrics_for_prompt(financial_metrics) -> str:
    """Format financial metrics for LLM prompt"""
    if not financial_metrics:
        return "No financial metrics available"
    
    metrics_text = []
    
    # Profitability metrics
    if financial_metrics.return_on_equity:
        metrics_text.append(f"Return on Equity: {financial_metrics.return_on_equity:.2%}")
    if financial_metrics.net_margin:
        metrics_text.append(f"Net Margin: {financial_metrics.net_margin:.2%}")
    if financial_metrics.operating_margin:
        metrics_text.append(f"Operating Margin: {financial_metrics.operating_margin:.2%}")
    
    # Growth metrics
    if financial_metrics.revenue_growth:
        metrics_text.append(f"Revenue Growth: {financial_metrics.revenue_growth:.2%}")
    if financial_metrics.earnings_growth:
        metrics_text.append(f"Earnings Growth: {financial_metrics.earnings_growth:.2%}")
    
    # Financial health
    if financial_metrics.current_ratio:
        metrics_text.append(f"Current Ratio: {financial_metrics.current_ratio:.2f}")
    if financial_metrics.debt_to_equity:
        metrics_text.append(f"Debt to Equity: {financial_metrics.debt_to_equity:.2f}")
    
    # Valuation metrics
    if financial_metrics.price_to_earnings_ratio:
        metrics_text.append(f"P/E Ratio: {financial_metrics.price_to_earnings_ratio:.2f}")
    if financial_metrics.price_to_book_ratio:
        metrics_text.append(f"P/B Ratio: {financial_metrics.price_to_book_ratio:.2f}")
    
    return "\n".join(metrics_text) if metrics_text else "Limited financial metrics available"


def format_price_summary_for_prompt(price_data) -> str:
    """Format price data summary for LLM prompt"""
    if not price_data or len(price_data) == 0:
        return "No price data available"
    
    latest = price_data[-1]
    
    summary = [
        f"Latest Close: ₹{latest.close:.2f}",
        f"Latest Volume: {latest.volume:,}",
    ]
    
    if len(price_data) >= 5:
        week_ago = price_data[-5]
        week_change = ((latest.close - week_ago.close) / week_ago.close) * 100
        summary.append(f"5-day change: {week_change:.2f}%")
    
    if len(price_data) >= 30:
        month_ago = price_data[-30]
        month_change = ((latest.close - month_ago.close) / month_ago.close) * 100
        summary.append(f"30-day change: {month_change:.2f}%")
    
    # Calculate recent high/low
    recent_prices = [p.close for p in price_data[-30:]] if len(price_data) >= 30 else [p.close for p in price_data]
    recent_high = max(recent_prices)
    recent_low = min(recent_prices)
    
    summary.extend([
        f"Recent High: ₹{recent_high:.2f}",
        f"Recent Low: ₹{recent_low:.2f}"
    ])
    
    return "\n".join(summary)
