"""
Fundamental Analysis Agent
"""
import json
from typing import Dict, Any, Optional
from pydantic import BaseModel
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.ai_agent.data_adapters.models import StockAnalysisData, AnalysisSignal
from app.ai_agent.llm.prompts import FUNDAMENTAL_ANALYSIS_PROMPT
from app.ai_agent.utils.llm_helper import call_llm_with_json_response, format_financial_metrics_for_prompt


class FundamentalAnalysisResponse(BaseModel):
    """Response model for fundamental analysis"""
    signal: str  # "bullish", "bearish", "neutral"
    confidence: float  # 0.0 to 1.0
    reasoning: Dict[str, Any]


class FundamentalAnalysisAgent:
    """Agent for performing fundamental analysis"""
    
    def __init__(self):
        self.name = "fundamental_analyst"
    
    def analyze(self, stock_data: StockAnalysisData) -> Optional[AnalysisSignal]:
        """
        Perform fundamental analysis on stock data
        
        Args:
            stock_data: Complete stock analysis data
            
        Returns:
            AnalysisSignal with fundamental analysis results
        """
        try:
            # Prepare prompt variables
            prompt_variables = {
                "company_name": stock_data.company_data.company_name,
                "symbol": stock_data.symbol,
                "sector": stock_data.company_data.sector,
                "financial_metrics": format_financial_metrics_for_prompt(stock_data.financial_metrics),
                "current_price": f"₹{stock_data.company_data.current_price:.2f}" if stock_data.company_data.current_price else "N/A",
                "market_cap": f"₹{stock_data.company_data.market_cap_in_cr:.2f} Cr" if stock_data.company_data.market_cap_in_cr else "N/A"
            }
            
            # Call LLM for analysis
            response = call_llm_with_json_response(
                prompt=FUNDAMENTAL_ANALYSIS_PROMPT,
                prompt_variables=prompt_variables,
                response_model=FundamentalAnalysisResponse
            )
            
            if not response:
                return self._create_fallback_analysis(stock_data)
            
            # Create analysis signal
            return AnalysisSignal(
                signal=response.signal,
                confidence=response.confidence,
                reasoning=response.reasoning
            )
            
        except Exception as e:
            print(f"Error in fundamental analysis for {stock_data.symbol}: {e}")
            return self._create_fallback_analysis(stock_data)
    
    def _create_fallback_analysis(self, stock_data: StockAnalysisData) -> AnalysisSignal:
        """Create fallback analysis when LLM fails"""
        # Simple rule-based analysis as fallback
        signals = []
        reasoning = {}
        
        metrics = stock_data.financial_metrics
        
        # Profitability check
        if metrics.return_on_equity and metrics.return_on_equity > 0.15:
            signals.append("bullish")
            reasoning["profitability"] = "Strong ROE above 15%"
        elif metrics.return_on_equity and metrics.return_on_equity < 0.05:
            signals.append("bearish")
            reasoning["profitability"] = "Weak ROE below 5%"
        else:
            signals.append("neutral")
            reasoning["profitability"] = "Moderate profitability metrics"
        
        # Growth check
        if metrics.revenue_growth and metrics.revenue_growth > 0.10:
            signals.append("bullish")
            reasoning["growth"] = "Strong revenue growth above 10%"
        elif metrics.revenue_growth and metrics.revenue_growth < 0:
            signals.append("bearish")
            reasoning["growth"] = "Negative revenue growth"
        else:
            signals.append("neutral")
            reasoning["growth"] = "Moderate growth metrics"
        
        # Valuation check
        if metrics.price_to_earnings_ratio and metrics.price_to_earnings_ratio < 15:
            signals.append("bullish")
            reasoning["valuation"] = "Attractive P/E ratio below 15"
        elif metrics.price_to_earnings_ratio and metrics.price_to_earnings_ratio > 30:
            signals.append("bearish")
            reasoning["valuation"] = "High P/E ratio above 30"
        else:
            signals.append("neutral")
            reasoning["valuation"] = "Moderate valuation metrics"
        
        # Determine overall signal
        bullish_count = signals.count("bullish")
        bearish_count = signals.count("bearish")
        
        if bullish_count > bearish_count:
            overall_signal = "bullish"
            confidence = 0.6
        elif bearish_count > bullish_count:
            overall_signal = "bearish"
            confidence = 0.6
        else:
            overall_signal = "neutral"
            confidence = 0.5
        
        reasoning["fallback_analysis"] = "Analysis performed using rule-based fallback due to LLM unavailability"
        
        return AnalysisSignal(
            signal=overall_signal,
            confidence=confidence,
            reasoning=reasoning
        )
