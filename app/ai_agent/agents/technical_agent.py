"""
Technical Analysis Agent
"""
import json
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
from pydantic import BaseModel
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.ai_agent.data_adapters.models import StockAnalysisData, AnalysisSignal
from app.ai_agent.llm.prompts import TECHNICAL_ANALYSIS_PROMPT
from app.ai_agent.utils.llm_helper import call_llm_with_json_response, format_price_summary_for_prompt


class TechnicalAnalysisResponse(BaseModel):
    """Response model for technical analysis"""
    signal: str  # "bullish", "bearish", "neutral"
    confidence: float  # 0.0 to 1.0
    reasoning: Dict[str, Any]


class TechnicalAnalysisAgent:
    """Agent for performing technical analysis"""
    
    def __init__(self):
        self.name = "technical_analyst"
    
    def analyze(self, stock_data: StockAnalysisData) -> Optional[AnalysisSignal]:
        """
        Perform technical analysis on stock data
        
        Args:
            stock_data: Complete stock analysis data
            
        Returns:
            AnalysisSignal with technical analysis results
        """
        try:
            # Convert price data to DataFrame for analysis
            df = self._create_price_dataframe(stock_data.price_data)
            if df is None or len(df) < 20:
                return self._create_insufficient_data_analysis()
            
            # Calculate technical indicators
            technical_indicators = self._calculate_technical_indicators(df)
            
            # Calculate price changes
            price_changes = self._calculate_price_changes(df)
            
            # Prepare prompt variables
            prompt_variables = {
                "symbol": stock_data.symbol,
                "current_price": f"₹{df['close'].iloc[-1]:.2f}",
                "price_change_1d": f"{price_changes['1d']:.2f}",
                "price_change_5d": f"{price_changes['5d']:.2f}",
                "price_change_30d": f"{price_changes['30d']:.2f}",
                "technical_indicators": self._format_technical_indicators(technical_indicators),
                "price_summary": format_price_summary_for_prompt(stock_data.price_data)
            }
            
            # Call LLM for analysis
            response = call_llm_with_json_response(
                prompt=TECHNICAL_ANALYSIS_PROMPT,
                prompt_variables=prompt_variables,
                response_model=TechnicalAnalysisResponse
            )
            
            if not response:
                return self._create_fallback_analysis(df, technical_indicators)
            
            # Create analysis signal
            return AnalysisSignal(
                signal=response.signal,
                confidence=response.confidence,
                reasoning=response.reasoning
            )
            
        except Exception as e:
            print(f"Error in technical analysis for {stock_data.symbol}: {e}")
            return self._create_fallback_analysis(df if 'df' in locals() else None, {})
    
    def _create_price_dataframe(self, price_data) -> Optional[pd.DataFrame]:
        """Convert price data to pandas DataFrame"""
        if not price_data:
            return None
        
        try:
            df_data = []
            for price in price_data:
                df_data.append({
                    'datetime': pd.to_datetime(price.datetime),
                    'open': price.open,
                    'high': price.high,
                    'low': price.low,
                    'close': price.close,
                    'volume': price.volume
                })
            
            df = pd.DataFrame(df_data)
            df.set_index('datetime', inplace=True)
            df.sort_index(inplace=True)
            return df
        except Exception as e:
            print(f"Error creating price dataframe: {e}")
            return None
    
    def _calculate_technical_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Calculate basic technical indicators"""
        indicators = {}
        
        try:
            # Moving averages
            indicators['sma_20'] = df['close'].rolling(window=20).mean().iloc[-1]
            indicators['sma_50'] = df['close'].rolling(window=50).mean().iloc[-1] if len(df) >= 50 else None
            indicators['ema_12'] = df['close'].ewm(span=12).mean().iloc[-1]
            indicators['ema_26'] = df['close'].ewm(span=26).mean().iloc[-1]
            
            # RSI
            indicators['rsi'] = self._calculate_rsi(df['close']).iloc[-1]
            
            # MACD
            macd_line = indicators['ema_12'] - indicators['ema_26']
            indicators['macd'] = macd_line
            
            # Bollinger Bands
            bb_upper, bb_lower = self._calculate_bollinger_bands(df['close'])
            indicators['bb_upper'] = bb_upper.iloc[-1]
            indicators['bb_lower'] = bb_lower.iloc[-1]
            indicators['bb_position'] = (df['close'].iloc[-1] - bb_lower.iloc[-1]) / (bb_upper.iloc[-1] - bb_lower.iloc[-1])
            
            # Volume analysis
            indicators['avg_volume'] = df['volume'].rolling(window=20).mean().iloc[-1]
            indicators['volume_ratio'] = df['volume'].iloc[-1] / indicators['avg_volume']
            
        except Exception as e:
            print(f"Error calculating technical indicators: {e}")
        
        return indicators
    
    def _calculate_price_changes(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate price changes over different periods"""
        changes = {}
        current_price = df['close'].iloc[-1]
        
        try:
            # 1-day change
            if len(df) >= 2:
                prev_price = df['close'].iloc[-2]
                changes['1d'] = ((current_price - prev_price) / prev_price) * 100
            else:
                changes['1d'] = 0.0
            
            # 5-day change
            if len(df) >= 6:
                price_5d_ago = df['close'].iloc[-6]
                changes['5d'] = ((current_price - price_5d_ago) / price_5d_ago) * 100
            else:
                changes['5d'] = 0.0
            
            # 30-day change
            if len(df) >= 31:
                price_30d_ago = df['close'].iloc[-31]
                changes['30d'] = ((current_price - price_30d_ago) / price_30d_ago) * 100
            else:
                changes['30d'] = 0.0
                
        except Exception as e:
            print(f"Error calculating price changes: {e}")
            changes = {'1d': 0.0, '5d': 0.0, '30d': 0.0}
        
        return changes
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_bollinger_bands(self, prices: pd.Series, window: int = 20, num_std: float = 2):
        """Calculate Bollinger Bands"""
        sma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper_band = sma + (std * num_std)
        lower_band = sma - (std * num_std)
        return upper_band, lower_band
    
    def _format_technical_indicators(self, indicators: Dict[str, Any]) -> str:
        """Format technical indicators for prompt"""
        formatted = []
        
        for key, value in indicators.items():
            if value is not None:
                if isinstance(value, float):
                    formatted.append(f"{key}: {value:.2f}")
                else:
                    formatted.append(f"{key}: {value}")
        
        return "\n".join(formatted) if formatted else "Limited technical indicators available"
    
    def _create_fallback_analysis(self, df: Optional[pd.DataFrame], indicators: Dict[str, Any]) -> AnalysisSignal:
        """Create fallback analysis when LLM fails"""
        if df is None or len(df) < 5:
            return self._create_insufficient_data_analysis()
        
        signals = []
        reasoning = {}
        
        current_price = df['close'].iloc[-1]
        
        # Simple trend analysis
        if 'sma_20' in indicators and indicators['sma_20']:
            if current_price > indicators['sma_20']:
                signals.append("bullish")
                reasoning["trend"] = "Price above 20-day moving average"
            else:
                signals.append("bearish")
                reasoning["trend"] = "Price below 20-day moving average"
        
        # RSI analysis
        if 'rsi' in indicators and indicators['rsi']:
            rsi = indicators['rsi']
            if rsi < 30:
                signals.append("bullish")
                reasoning["momentum"] = f"RSI oversold at {rsi:.1f}"
            elif rsi > 70:
                signals.append("bearish")
                reasoning["momentum"] = f"RSI overbought at {rsi:.1f}"
            else:
                signals.append("neutral")
                reasoning["momentum"] = f"RSI neutral at {rsi:.1f}"
        
        # Volume analysis
        if 'volume_ratio' in indicators and indicators['volume_ratio']:
            if indicators['volume_ratio'] > 1.5:
                reasoning["volume"] = "High volume activity"
            else:
                reasoning["volume"] = "Normal volume activity"
        
        # Determine overall signal
        bullish_count = signals.count("bullish")
        bearish_count = signals.count("bearish")
        
        if bullish_count > bearish_count:
            overall_signal = "bullish"
            confidence = 0.6
        elif bearish_count > bullish_count:
            overall_signal = "bearish"
            confidence = 0.6
        else:
            overall_signal = "neutral"
            confidence = 0.5
        
        reasoning["fallback_analysis"] = "Analysis performed using rule-based fallback"
        
        return AnalysisSignal(
            signal=overall_signal,
            confidence=confidence,
            reasoning=reasoning
        )
    
    def _create_insufficient_data_analysis(self) -> AnalysisSignal:
        """Create analysis when insufficient data is available"""
        return AnalysisSignal(
            signal="neutral",
            confidence=0.3,
            reasoning={
                "data_limitation": "Insufficient price data for comprehensive technical analysis",
                "recommendation": "Collect more historical data for better analysis"
            }
        )
