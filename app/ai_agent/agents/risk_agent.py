"""
Risk Management Agent
"""
import json
from typing import Dict, Any, Optional
from pydantic import BaseModel
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.ai_agent.data_adapters.models import StockAnalysisData, AnalysisSignal
from app.ai_agent.llm.prompts import RISK_MANAGEMENT_PROMPT
from app.ai_agent.utils.llm_helper import call_llm_with_json_response


class RiskAnalysisResponse(BaseModel):
    """Response model for risk analysis"""
    risk_level: str  # "low", "medium", "high"
    confidence: float  # 0.0 to 1.0
    reasoning: Dict[str, Any]
    max_position_percentage: float  # 0.0 to 1.0


class RiskManagementAgent:
    """Agent for performing risk analysis"""
    
    def __init__(self):
        self.name = "risk_manager"
    
    def analyze(self, stock_data: StockAnalysisData, fundamental_signals: Optional[AnalysisSignal] = None, 
                technical_signals: Optional[AnalysisSignal] = None) -> Optional[AnalysisSignal]:
        """
        Perform risk analysis on stock data
        
        Args:
            stock_data: Complete stock analysis data
            fundamental_signals: Fundamental analysis results
            technical_signals: Technical analysis results
            
        Returns:
            AnalysisSignal with risk analysis results
        """
        try:
            # Prepare financial health indicators
            financial_health = self._assess_financial_health(stock_data)
            
            # Prepare market conditions assessment
            market_conditions = self._assess_market_conditions(stock_data)
            
            # Prepare prompt variables
            prompt_variables = {
                "company_name": stock_data.company_data.company_name,
                "symbol": stock_data.symbol,
                "sector": stock_data.company_data.sector,
                "current_price": f"₹{stock_data.company_data.current_price:.2f}" if stock_data.company_data.current_price else "N/A",
                "fundamental_signals": self._format_signals(fundamental_signals),
                "technical_signals": self._format_signals(technical_signals),
                "financial_health": financial_health,
                "market_conditions": market_conditions
            }
            
            # Call LLM for analysis
            response = call_llm_with_json_response(
                prompt=RISK_MANAGEMENT_PROMPT,
                prompt_variables=prompt_variables,
                response_model=RiskAnalysisResponse
            )
            
            if not response:
                return self._create_fallback_analysis(stock_data, fundamental_signals, technical_signals)
            
            # Create analysis signal with risk level as signal
            return AnalysisSignal(
                signal=response.risk_level,
                confidence=response.confidence,
                reasoning=response.reasoning
            )
            
        except Exception as e:
            print(f"Error in risk analysis for {stock_data.symbol}: {e}")
            return self._create_fallback_analysis(stock_data, fundamental_signals, technical_signals)
    
    def _assess_financial_health(self, stock_data: StockAnalysisData) -> str:
        """Assess financial health of the company"""
        health_indicators = []
        metrics = stock_data.financial_metrics
        
        # Profitability assessment
        if metrics.return_on_equity:
            if metrics.return_on_equity > 0.15:
                health_indicators.append("Strong ROE")
            elif metrics.return_on_equity < 0.05:
                health_indicators.append("Weak ROE")
            else:
                health_indicators.append("Moderate ROE")
        
        # Debt assessment
        if metrics.debt_to_equity:
            if metrics.debt_to_equity < 0.3:
                health_indicators.append("Low debt levels")
            elif metrics.debt_to_equity > 1.0:
                health_indicators.append("High debt levels")
            else:
                health_indicators.append("Moderate debt levels")
        
        # Growth assessment
        if metrics.revenue_growth:
            if metrics.revenue_growth > 0.10:
                health_indicators.append("Strong revenue growth")
            elif metrics.revenue_growth < 0:
                health_indicators.append("Declining revenue")
            else:
                health_indicators.append("Stable revenue")
        
        return "; ".join(health_indicators) if health_indicators else "Limited financial health data"
    
    def _assess_market_conditions(self, stock_data: StockAnalysisData) -> str:
        """Assess market conditions affecting the stock"""
        conditions = []
        
        # Sector assessment
        conditions.append(f"Sector: {stock_data.company_data.sector}")
        
        # Market cap assessment
        if stock_data.company_data.market_cap_in_cr:
            if stock_data.company_data.market_cap_in_cr > 50000:  # > 50,000 Cr
                conditions.append("Large cap stock")
            elif stock_data.company_data.market_cap_in_cr > 5000:  # 5,000-50,000 Cr
                conditions.append("Mid cap stock")
            else:
                conditions.append("Small cap stock")
        
        # Price volatility assessment (basic)
        if len(stock_data.price_data) >= 30:
            import pandas as pd
            recent_prices = [p.close for p in stock_data.price_data[-30:]]
            price_std = pd.Series(recent_prices).std()
            avg_price = pd.Series(recent_prices).mean()
            volatility = (price_std / avg_price) * 100

            if volatility > 5:
                conditions.append("High volatility")
            elif volatility < 2:
                conditions.append("Low volatility")
            else:
                conditions.append("Moderate volatility")
        
        return "; ".join(conditions)
    
    def _format_signals(self, signals: Optional[AnalysisSignal]) -> str:
        """Format analysis signals for prompt"""
        if not signals:
            return "No signals available"
        
        return f"Signal: {signals.signal}, Confidence: {signals.confidence:.2f}"
    
    def _create_fallback_analysis(self, stock_data: StockAnalysisData, 
                                  fundamental_signals: Optional[AnalysisSignal] = None,
                                  technical_signals: Optional[AnalysisSignal] = None) -> AnalysisSignal:
        """Create fallback risk analysis when LLM fails"""
        risk_factors = []
        reasoning = {}
        
        # Assess based on available signals
        signal_risk = 0  # 0 = low risk, 1 = medium risk, 2 = high risk
        
        # Check fundamental signals
        if fundamental_signals:
            if fundamental_signals.signal == "bearish" and fundamental_signals.confidence > 0.7:
                signal_risk += 1
                risk_factors.append("Bearish fundamental signals")
            elif fundamental_signals.confidence < 0.5:
                signal_risk += 1
                risk_factors.append("Low confidence in fundamental analysis")
        
        # Check technical signals
        if technical_signals:
            if technical_signals.signal == "bearish" and technical_signals.confidence > 0.7:
                signal_risk += 1
                risk_factors.append("Bearish technical signals")
            elif technical_signals.confidence < 0.5:
                signal_risk += 1
                risk_factors.append("Low confidence in technical analysis")
        
        # Check financial metrics
        metrics = stock_data.financial_metrics
        if metrics.debt_to_equity and metrics.debt_to_equity > 1.0:
            signal_risk += 1
            risk_factors.append("High debt-to-equity ratio")
        
        if metrics.return_on_equity and metrics.return_on_equity < 0.05:
            signal_risk += 1
            risk_factors.append("Low return on equity")
        
        # Determine risk level
        if signal_risk >= 3:
            risk_level = "high"
            confidence = 0.7
            max_position = 0.05  # 5% max position
        elif signal_risk >= 1:
            risk_level = "medium"
            confidence = 0.6
            max_position = 0.10  # 10% max position
        else:
            risk_level = "low"
            confidence = 0.6
            max_position = 0.15  # 15% max position
        
        reasoning.update({
            "risk_factors": risk_factors,
            "risk_assessment": f"Risk level determined based on {signal_risk} risk factors",
            "max_position_percentage": max_position,
            "fallback_analysis": "Risk analysis performed using rule-based fallback"
        })
        
        return AnalysisSignal(
            signal=risk_level,
            confidence=confidence,
            reasoning=reasoning
        )
