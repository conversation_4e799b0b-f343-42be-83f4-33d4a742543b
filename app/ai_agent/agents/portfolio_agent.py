"""
Portfolio Management Agent
"""
import json
from typing import Dict, Any, Optional
from pydantic import BaseModel
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.ai_agent.data_adapters.models import StockAnalysisData, AnalysisSignal
from app.ai_agent.llm.prompts import PORTFOLIO_MANAGEMENT_PROMPT
from app.ai_agent.utils.llm_helper import call_llm_with_json_response


class PortfolioRecommendationResponse(BaseModel):
    """Response model for portfolio recommendation"""
    recommendation: str  # "strong_buy", "buy", "hold", "sell", "strong_sell"
    confidence: float  # 0.0 to 1.0
    reasoning: Dict[str, Any]
    position_size_recommendation: str
    monitoring_points: list


class PortfolioManagementAgent:
    """Agent for making final portfolio recommendations"""
    
    def __init__(self):
        self.name = "portfolio_manager"
    
    def analyze(self, stock_data: StockAnalysisData, 
                fundamental_signals: Optional[AnalysisSignal] = None,
                technical_signals: Optional[AnalysisSignal] = None,
                risk_signals: Optional[AnalysisSignal] = None,
                risk_tolerance: str = "medium",
                timeframe: str = "medium_term") -> Optional[AnalysisSignal]:
        """
        Make final portfolio recommendation
        
        Args:
            stock_data: Complete stock analysis data
            fundamental_signals: Fundamental analysis results
            technical_signals: Technical analysis results
            risk_signals: Risk analysis results
            risk_tolerance: Investor risk tolerance (low/medium/high)
            timeframe: Investment timeframe (short_term/medium_term/long_term)
            
        Returns:
            AnalysisSignal with portfolio recommendation
        """
        try:
            # Prepare prompt variables
            prompt_variables = {
                "symbol": stock_data.symbol,
                "company_name": stock_data.company_data.company_name,
                "current_price": f"₹{stock_data.company_data.current_price:.2f}" if stock_data.company_data.current_price else "N/A",
                "fundamental_analysis": self._format_analysis(fundamental_signals),
                "technical_analysis": self._format_analysis(technical_signals),
                "risk_assessment": self._format_analysis(risk_signals),
                "risk_tolerance": risk_tolerance,
                "timeframe": timeframe
            }
            
            # Call LLM for analysis
            response = call_llm_with_json_response(
                prompt=PORTFOLIO_MANAGEMENT_PROMPT,
                prompt_variables=prompt_variables,
                response_model=PortfolioRecommendationResponse
            )
            
            if not response:
                return self._create_fallback_recommendation(stock_data, fundamental_signals, technical_signals, risk_signals)
            
            # Create analysis signal with recommendation as signal
            return AnalysisSignal(
                signal=response.recommendation,
                confidence=response.confidence,
                reasoning=response.reasoning
            )
            
        except Exception as e:
            print(f"Error in portfolio recommendation for {stock_data.symbol}: {e}")
            return self._create_fallback_recommendation(stock_data, fundamental_signals, technical_signals, risk_signals)
    
    def _format_analysis(self, signals: Optional[AnalysisSignal]) -> str:
        """Format analysis signals for prompt"""
        if not signals:
            return "No analysis available"
        
        reasoning_summary = ""
        if isinstance(signals.reasoning, dict):
            # Extract key points from reasoning
            key_points = []
            for key, value in signals.reasoning.items():
                if isinstance(value, str) and len(value) < 100:
                    key_points.append(f"{key}: {value}")
                elif isinstance(value, list):
                    key_points.append(f"{key}: {', '.join(map(str, value[:3]))}")  # First 3 items
            reasoning_summary = "; ".join(key_points[:5])  # First 5 key points
        
        return f"Signal: {signals.signal}, Confidence: {signals.confidence:.2f}, Key Points: {reasoning_summary}"
    
    def _create_fallback_recommendation(self, stock_data: StockAnalysisData,
                                        fundamental_signals: Optional[AnalysisSignal] = None,
                                        technical_signals: Optional[AnalysisSignal] = None,
                                        risk_signals: Optional[AnalysisSignal] = None) -> AnalysisSignal:
        """Create fallback recommendation when LLM fails"""
        
        # Collect all signals
        signals = []
        confidences = []
        
        if fundamental_signals:
            signals.append(fundamental_signals.signal)
            confidences.append(fundamental_signals.confidence)
        
        if technical_signals:
            signals.append(technical_signals.signal)
            confidences.append(technical_signals.confidence)
        
        # Weight the signals
        bullish_weight = 0
        bearish_weight = 0
        neutral_weight = 0
        
        for i, signal in enumerate(signals):
            confidence = confidences[i] if i < len(confidences) else 0.5
            
            if signal == "bullish":
                bullish_weight += confidence
            elif signal == "bearish":
                bearish_weight += confidence
            else:
                neutral_weight += confidence
        
        # Consider risk level
        risk_adjustment = 0
        if risk_signals:
            if risk_signals.signal == "high":
                risk_adjustment = -0.3  # Reduce bullish sentiment
            elif risk_signals.signal == "low":
                risk_adjustment = 0.1   # Slight boost to bullish sentiment
        
        # Adjust weights based on risk
        bullish_weight += risk_adjustment
        bearish_weight -= risk_adjustment
        
        # Determine recommendation
        total_weight = bullish_weight + bearish_weight + neutral_weight
        
        if total_weight == 0:
            recommendation = "hold"
            confidence = 0.3
        else:
            bullish_ratio = bullish_weight / total_weight
            bearish_ratio = bearish_weight / total_weight
            
            if bullish_ratio > 0.6:
                recommendation = "buy" if bullish_ratio > 0.8 else "buy"
                confidence = min(bullish_ratio, 0.8)
            elif bearish_ratio > 0.6:
                recommendation = "sell" if bearish_ratio > 0.8 else "sell"
                confidence = min(bearish_ratio, 0.8)
            else:
                recommendation = "hold"
                confidence = 0.5
        
        # Create reasoning
        reasoning = {
            "decision_rationale": f"Recommendation based on signal aggregation: {len(signals)} signals analyzed",
            "signal_summary": {
                "fundamental": fundamental_signals.signal if fundamental_signals else "N/A",
                "technical": technical_signals.signal if technical_signals else "N/A",
                "risk": risk_signals.signal if risk_signals else "N/A"
            },
            "confidence_factors": f"Bullish weight: {bullish_weight:.2f}, Bearish weight: {bearish_weight:.2f}",
            "risk_adjustment": f"Risk adjustment applied: {risk_adjustment:.2f}",
            "fallback_analysis": "Recommendation generated using rule-based fallback method"
        }
        
        # Add price targets if possible
        if stock_data.company_data.current_price:
            current_price = stock_data.company_data.current_price
            if recommendation in ["buy", "strong_buy"]:
                reasoning["price_targets"] = {
                    "target_price": current_price * 1.15,  # 15% upside
                    "stop_loss": current_price * 0.90,     # 10% downside
                }
            elif recommendation in ["sell", "strong_sell"]:
                reasoning["price_targets"] = {
                    "target_price": current_price * 0.85,  # 15% downside
                    "stop_loss": current_price * 1.10,     # 10% upside (for short)
                }
        
        return AnalysisSignal(
            signal=recommendation,
            confidence=confidence,
            reasoning=reasoning
        )
