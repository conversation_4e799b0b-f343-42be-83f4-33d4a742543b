"""
LangGraph workflow definition for AI stock analysis
"""
from langgraph.graph import State<PERSON>raph, END
from app.ai_agent.workflow.state import AnalysisState, create_initial_state
from app.ai_agent.workflow.nodes import (
    data_collection_node,
    fundamental_analysis_node,
    technical_analysis_node,
    risk_analysis_node,
    portfolio_recommendation_node,
    finalize_results_node
)


def create_analysis_workflow() -> StateGraph:
    """
    Create the AI stock analysis workflow using LangGraph
    
    Returns:
        StateGraph: Compiled workflow graph
    """
    # Create the workflow graph
    workflow = StateGraph(AnalysisState)
    
    # Add nodes
    workflow.add_node("data_collection", data_collection_node)
    workflow.add_node("fundamental_analysis", fundamental_analysis_node)
    workflow.add_node("technical_analysis", technical_analysis_node)
    workflow.add_node("risk_analysis", risk_analysis_node)
    workflow.add_node("portfolio_recommendation", portfolio_recommendation_node)
    workflow.add_node("finalize_results", finalize_results_node)
    
    # Define the workflow edges (sequential execution)
    workflow.add_edge("data_collection", "fundamental_analysis")
    workflow.add_edge("fundamental_analysis", "technical_analysis")
    workflow.add_edge("technical_analysis", "risk_analysis")
    workflow.add_edge("risk_analysis", "portfolio_recommendation")
    workflow.add_edge("portfolio_recommendation", "finalize_results")
    workflow.add_edge("finalize_results", END)
    
    # Set entry point
    workflow.set_entry_point("data_collection")
    
    return workflow


def run_stock_analysis(symbol: str, risk_tolerance: str = "medium",
                      investment_timeframe: str = "medium_term") -> AnalysisState:
    """
    Run complete stock analysis for a given symbol

    Args:
        symbol: Stock symbol to analyze
        risk_tolerance: Risk tolerance level (low/medium/high)
        investment_timeframe: Investment timeframe (short_term/medium_term/long_term)

    Returns:
        AnalysisState: Final state with all analysis results
    """
    # Create workflow
    workflow = create_analysis_workflow()
    app = workflow.compile()

    # Create initial state
    initial_state = create_initial_state(symbol, risk_tolerance, investment_timeframe)

    # Run the workflow
    try:
        final_state = app.invoke(initial_state)
        return final_state
    except Exception as e:
        print(f"Error running analysis workflow: {e}")
        from app.ai_agent.workflow.state import add_error
        add_error(initial_state, f"Workflow execution failed: {str(e)}")
        return initial_state


def run_batch_analysis(symbols: list, risk_tolerance: str = "medium", 
                      investment_timeframe: str = "medium_term") -> dict:
    """
    Run analysis for multiple symbols
    
    Args:
        symbols: List of stock symbols to analyze
        risk_tolerance: Risk tolerance level
        investment_timeframe: Investment timeframe
    
    Returns:
        dict: Results for each symbol
    """
    results = {}
    
    for symbol in symbols:
        print(f"\n🚀 Starting analysis for {symbol}")
        print("=" * 50)
        
        try:
            result = run_stock_analysis(symbol, risk_tolerance, investment_timeframe)
            results[symbol] = result
            
            if result["final_recommendation"]:
                recommendation = result["final_recommendation"]["recommendation"]["action"]
                confidence = result["final_recommendation"]["recommendation"]["confidence"]
                print(f"📊 Final recommendation for {symbol}: {recommendation.upper()} (confidence: {confidence:.2f})")
            else:
                print(f"❌ Analysis failed for {symbol}")

        except Exception as e:
            print(f"❌ Error analyzing {symbol}: {e}")
            # Create error state
            error_state = create_initial_state(symbol)
            from app.ai_agent.workflow.state import add_error
            add_error(error_state, f"Analysis failed: {str(e)}")
            results[symbol] = error_state
        
        print("=" * 50)
    
    return results
