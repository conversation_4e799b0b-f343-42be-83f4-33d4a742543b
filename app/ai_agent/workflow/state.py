"""
Workflow state management for AI stock analysis
"""
from typing import Dict, Any, Optional, List
try:
    from typing import TypedDict
except ImportError:
    from typing_extensions import TypedDict
from app.ai_agent.data_adapters.models import StockAnalysisData, AnalysisSignal


class AnalysisState(TypedDict):
    """State object for the analysis workflow"""

    # Input data
    symbol: str
    stock_data: Optional[StockAnalysisData]

    # Analysis results
    fundamental_result: Optional[AnalysisSignal]
    technical_result: Optional[AnalysisSignal]
    risk_result: Optional[AnalysisSignal]
    portfolio_result: Optional[AnalysisSignal]

    # Configuration
    risk_tolerance: str  # low, medium, high
    investment_timeframe: str  # short_term, medium_term, long_term

    # Workflow metadata
    current_step: str
    completed_steps: List[str]
    errors: List[str]

    # Final results
    final_recommendation: Optional[Dict[str, Any]]


def add_error(state: AnalysisState, error: str) -> AnalysisState:
    """Add an error to the state"""
    state["errors"].append(error)
    return state


def mark_step_complete(state: AnalysisState, step: str) -> AnalysisState:
    """Mark a step as completed"""
    if step not in state["completed_steps"]:
        state["completed_steps"].append(step)
    return state


def is_step_complete(state: AnalysisState, step: str) -> bool:
    """Check if a step is completed"""
    return step in state["completed_steps"]


def get_analysis_summary(state: AnalysisState) -> Dict[str, Any]:
    """Get a summary of all analysis results"""
    return {
        "symbol": state["symbol"],
        "fundamental": {
            "signal": state["fundamental_result"].signal if state["fundamental_result"] else None,
            "confidence": state["fundamental_result"].confidence if state["fundamental_result"] else None
        },
        "technical": {
            "signal": state["technical_result"].signal if state["technical_result"] else None,
            "confidence": state["technical_result"].confidence if state["technical_result"] else None
        },
        "risk": {
            "signal": state["risk_result"].signal if state["risk_result"] else None,
            "confidence": state["risk_result"].confidence if state["risk_result"] else None
        },
        "recommendation": {
            "signal": state["portfolio_result"].signal if state["portfolio_result"] else None,
            "confidence": state["portfolio_result"].confidence if state["portfolio_result"] else None
        },
        "completed_steps": state["completed_steps"],
        "errors": state["errors"]
    }


def create_initial_state(symbol: str, risk_tolerance: str = "medium",
                        investment_timeframe: str = "medium_term") -> AnalysisState:
    """Create initial analysis state"""
    return AnalysisState(
        symbol=symbol.upper(),
        stock_data=None,
        fundamental_result=None,
        technical_result=None,
        risk_result=None,
        portfolio_result=None,
        risk_tolerance=risk_tolerance,
        investment_timeframe=investment_timeframe,
        current_step="start",
        completed_steps=[],
        errors=[],
        final_recommendation=None
    )
