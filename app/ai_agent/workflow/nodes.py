"""
Workflow nodes for the AI stock analysis system
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.ai_agent.workflow.state import AnalysisState, add_error, mark_step_complete
from app.ai_agent.data_adapters.data_service import DataService
from app.ai_agent.agents.fundamental_agent import FundamentalAnalysisAgent
from app.ai_agent.agents.technical_agent import TechnicalAnalysisAgent
from app.ai_agent.agents.risk_agent import RiskManagementAgent
from app.ai_agent.agents.portfolio_agent import PortfolioManagementAgent


def data_collection_node(state: AnalysisState) -> AnalysisState:
    """
    Node to collect all necessary data for analysis
    """
    print(f"📊 Collecting data for {state['symbol']}...")

    try:
        data_service = DataService()
        stock_data = data_service.get_stock_analysis_data(state["symbol"])

        if stock_data:
            state["stock_data"] = stock_data
            mark_step_complete(state, "data_collection")
            print(f"✅ Data collection completed for {state['symbol']}")
        else:
            error_msg = f"Failed to collect data for {state['symbol']}"
            add_error(state, error_msg)
            print(f"❌ {error_msg}")

    except Exception as e:
        error_msg = f"Error in data collection: {str(e)}"
        add_error(state, error_msg)
        print(f"❌ {error_msg}")

    state["current_step"] = "fundamental_analysis"
    return state


def fundamental_analysis_node(state: AnalysisState) -> AnalysisState:
    """
    Node to perform fundamental analysis
    """
    print(f"📈 Performing fundamental analysis for {state['symbol']}...")

    if not state["stock_data"]:
        error_msg = "No stock data available for fundamental analysis"
        add_error(state, error_msg)
        print(f"❌ {error_msg}")
        state["current_step"] = "technical_analysis"
        return state

    try:
        agent = FundamentalAnalysisAgent()
        analysis = agent.analyze(state["stock_data"])

        if analysis:
            state["fundamental_result"] = analysis
            mark_step_complete(state, "fundamental_analysis")
            print(f"✅ Fundamental analysis completed: {analysis.signal} (confidence: {analysis.confidence:.2f})")
        else:
            error_msg = "Fundamental analysis failed"
            add_error(state, error_msg)
            print(f"❌ {error_msg}")

    except Exception as e:
        error_msg = f"Error in fundamental analysis: {str(e)}"
        add_error(state, error_msg)
        print(f"❌ {error_msg}")

    state["current_step"] = "technical_analysis"
    return state


def technical_analysis_node(state: AnalysisState) -> AnalysisState:
    """
    Node to perform technical analysis
    """
    print(f"📊 Performing technical analysis for {state['symbol']}...")

    if not state["stock_data"]:
        error_msg = "No stock data available for technical analysis"
        add_error(state, error_msg)
        print(f"❌ {error_msg}")
        state["current_step"] = "risk_analysis"
        return state

    try:
        agent = TechnicalAnalysisAgent()
        analysis = agent.analyze(state["stock_data"])

        if analysis:
            state["technical_result"] = analysis
            mark_step_complete(state, "technical_analysis")
            print(f"✅ Technical analysis completed: {analysis.signal} (confidence: {analysis.confidence:.2f})")
        else:
            error_msg = "Technical analysis failed"
            add_error(state, error_msg)
            print(f"❌ {error_msg}")

    except Exception as e:
        error_msg = f"Error in technical analysis: {str(e)}"
        add_error(state, error_msg)
        print(f"❌ {error_msg}")

    state["current_step"] = "risk_analysis"
    return state


def risk_analysis_node(state: AnalysisState) -> AnalysisState:
    """
    Node to perform risk analysis
    """
    print(f"⚠️ Performing risk analysis for {state['symbol']}...")

    if not state["stock_data"]:
        error_msg = "No stock data available for risk analysis"
        add_error(state, error_msg)
        print(f"❌ {error_msg}")
        state["current_step"] = "portfolio_recommendation"
        return state

    try:
        agent = RiskManagementAgent()
        analysis = agent.analyze(
            state["stock_data"],
            state["fundamental_result"],
            state["technical_result"]
        )

        if analysis:
            state["risk_result"] = analysis
            mark_step_complete(state, "risk_analysis")
            print(f"✅ Risk analysis completed: {analysis.signal} risk (confidence: {analysis.confidence:.2f})")
        else:
            error_msg = "Risk analysis failed"
            add_error(state, error_msg)
            print(f"❌ {error_msg}")

    except Exception as e:
        error_msg = f"Error in risk analysis: {str(e)}"
        add_error(state, error_msg)
        print(f"❌ {error_msg}")

    state["current_step"] = "portfolio_recommendation"
    return state


def portfolio_recommendation_node(state: AnalysisState) -> AnalysisState:
    """
    Node to generate final portfolio recommendation
    """
    print(f"💼 Generating portfolio recommendation for {state['symbol']}...")

    if not state["stock_data"]:
        error_msg = "No stock data available for portfolio recommendation"
        add_error(state, error_msg)
        print(f"❌ {error_msg}")
        state["current_step"] = "complete"
        return state

    try:
        agent = PortfolioManagementAgent()
        recommendation = agent.analyze(
            state["stock_data"],
            state["fundamental_result"],
            state["technical_result"],
            state["risk_result"],
            state["risk_tolerance"],
            state["investment_timeframe"]
        )

        if recommendation:
            state["portfolio_result"] = recommendation
            mark_step_complete(state, "portfolio_recommendation")
            print(f"✅ Portfolio recommendation completed: {recommendation.signal} (confidence: {recommendation.confidence:.2f})")
        else:
            error_msg = "Portfolio recommendation failed"
            add_error(state, error_msg)
            print(f"❌ {error_msg}")

    except Exception as e:
        error_msg = f"Error in portfolio recommendation: {str(e)}"
        add_error(state, error_msg)
        print(f"❌ {error_msg}")

    state["current_step"] = "complete"
    return state


def finalize_results_node(state: AnalysisState) -> AnalysisState:
    """
    Node to finalize and format results
    """
    print(f"📋 Finalizing results for {state['symbol']}...")

    try:
        from app.ai_agent.workflow.state import get_analysis_summary

        # Create comprehensive final recommendation
        final_recommendation = {
            "symbol": state["symbol"],
            "company_name": state["stock_data"].company_data.company_name if state["stock_data"] else "Unknown",
            "current_price": state["stock_data"].company_data.current_price if state["stock_data"] else None,
            "analysis_summary": get_analysis_summary(state),
            "recommendation": {
                "action": state["portfolio_result"].signal if state["portfolio_result"] else "hold",
                "confidence": state["portfolio_result"].confidence if state["portfolio_result"] else 0.0,
                "reasoning": state["portfolio_result"].reasoning if state["portfolio_result"] else {}
            },
            "risk_level": state["risk_result"].signal if state["risk_result"] else "unknown",
            "analysis_quality": {
                "completed_steps": len(state["completed_steps"]),
                "total_steps": 4,  # data_collection, fundamental, technical, risk, portfolio
                "errors": len(state["errors"]),
                "success_rate": len(state["completed_steps"]) / 5 * 100  # 5 total steps including finalization
            }
        }

        state["final_recommendation"] = final_recommendation
        mark_step_complete(state, "finalization")
        print(f"✅ Analysis completed for {state['symbol']}")

    except Exception as e:
        error_msg = f"Error in finalizing results: {str(e)}"
        add_error(state, error_msg)
        print(f"❌ {error_msg}")

    state["current_step"] = "complete"
    return state
