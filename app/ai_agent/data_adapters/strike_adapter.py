"""
Adapter to convert StrikeApi data to AI analysis format
"""
from typing import List, Optional
from datetime import datetime, timedelta
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.data.strike_api import StrikeAPI
from app.ai_agent.data_adapters.models import PriceData
import pandas as pd


class StrikeDataAdapter:
    """Adapter to convert StrikeAPI data for AI analysis"""
    
    def __init__(self):
        self.api = StrikeAPI()
    
    def get_price_data(self, symbol: str, days_back: int = 365) -> List[PriceData]:
        """Get price data from StrikeAPI"""
        try:
            price_ticks = self.api.get_price_data_simple(symbol, days_back)
            if not price_ticks:
                return []
            
            # Convert to PriceData format
            price_data = []
            for tick in price_ticks:
                price_data.append(PriceData(
                    datetime=tick.datetime,
                    open=tick.open,
                    high=tick.high,
                    low=tick.low,
                    close=tick.close,
                    volume=tick.volume,
                    delivery_volume=tick.delivery_volume
                ))
            
            return price_data
        except Exception as e:
            print(f"Error getting price data for {symbol}: {e}")
            return []
    
    def get_price_dataframe(self, symbol: str, days_back: int = 365) -> Optional[pd.DataFrame]:
        """Get price data as pandas DataFrame for technical analysis"""
        try:
            price_data = self.get_price_data(symbol, days_back)
            if not price_data:
                return None
            
            # Convert to DataFrame
            df_data = []
            for price in price_data:
                df_data.append({
                    'datetime': price.datetime,
                    'open': price.open,
                    'high': price.high,
                    'low': price.low,
                    'close': price.close,
                    'volume': price.volume
                })
            
            df = pd.DataFrame(df_data)
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            df.sort_index(inplace=True)
            
            return df
        except Exception as e:
            print(f"Error creating price dataframe for {symbol}: {e}")
            return None
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """Get the latest price for a symbol"""
        try:
            price_data = self.get_price_data(symbol, days_back=5)  # Get recent data
            if price_data:
                return price_data[-1].close  # Return latest close price
        except Exception as e:
            print(f"Error getting latest price for {symbol}: {e}")
        return None
    
    def get_price_change(self, symbol: str, days: int = 1) -> Optional[dict]:
        """Get price change over specified days"""
        try:
            price_data = self.get_price_data(symbol, days_back=days + 5)
            if len(price_data) < days + 1:
                return None
            
            current_price = price_data[-1].close
            previous_price = price_data[-(days + 1)].close
            
            change = current_price - previous_price
            change_percent = (change / previous_price) * 100 if previous_price > 0 else 0
            
            return {
                'current_price': current_price,
                'previous_price': previous_price,
                'change': change,
                'change_percent': change_percent
            }
        except Exception as e:
            print(f"Error calculating price change for {symbol}: {e}")
        return None
