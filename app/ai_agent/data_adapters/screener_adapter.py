"""
Adapter to convert ScreenerScraper data to AI analysis format
"""
from typing import Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.data.screener_scraper import ScreenerScraper
from app.ai_agent.data_adapters.models import CompanyData, FinancialMetrics


class ScreenerDataAdapter:
    """Adapter to convert ScreenerScraper data for AI analysis"""
    
    def __init__(self):
        self.scraper = ScreenerScraper()
    
    def get_company_data(self, symbol: str) -> Optional[CompanyData]:
        """Get company information from ScreenerScraper"""
        try:
            stock_data = self.scraper.scrape_stock_data(symbol)
            if not stock_data:
                return None
            
            return CompanyData(
                symbol=symbol,
                company_name=stock_data.company_info.company_name,
                sector=stock_data.company_info.sector,
                about=stock_data.company_info.about,
                current_price=stock_data.key_metrics.current_price,
                market_cap_in_cr=stock_data.key_metrics.market_cap_in_cr
            )
        except Exception as e:
            print(f"Error getting company data for {symbol}: {e}")
            return None
    
    def get_financial_metrics(self, symbol: str) -> Optional[FinancialMetrics]:
        """Convert ScreenerScraper data to FinancialMetrics format"""
        try:
            stock_data = self.scraper.scrape_stock_data(symbol)
            if not stock_data:
                return None
            
            key_metrics = stock_data.key_metrics
            
            # Calculate growth metrics from financial data
            revenue_growth = self._calculate_revenue_growth(stock_data.financials.annual_results)
            earnings_growth = self._calculate_earnings_growth(stock_data.financials.annual_results)
            
            # Calculate financial ratios
            current_ratio = self._calculate_current_ratio(stock_data.financials.balance_sheets)
            debt_to_equity = self._calculate_debt_to_equity(stock_data.financials.balance_sheets)
            
            return FinancialMetrics(
                # Profitability metrics (calculated from ROE and margins)
                return_on_equity=key_metrics.roe_percentage / 100 if key_metrics.roe_percentage else None,
                return_on_assets=key_metrics.roce_percentage / 100 if key_metrics.roce_percentage else None,
                net_margin=self._calculate_net_margin(stock_data.financials.annual_results),
                operating_margin=self._calculate_operating_margin(stock_data.financials.annual_results),
                
                # Growth metrics
                revenue_growth=revenue_growth,
                earnings_growth=earnings_growth,
                
                # Financial health
                current_ratio=current_ratio,
                debt_to_equity=debt_to_equity,
                earnings_per_share=self._get_latest_eps(stock_data.financials.annual_results),
                
                # Valuation metrics
                price_to_earnings_ratio=key_metrics.stock_pe,
                price_to_book_ratio=self._calculate_pb_ratio(key_metrics),
                market_cap=key_metrics.market_cap_in_cr * 100 if key_metrics.market_cap_in_cr else None,  # Convert to millions
                
                # Additional metrics
                dividend_yield=key_metrics.dividend_yield_percentage / 100 if key_metrics.dividend_yield_percentage else None
            )
        except Exception as e:
            print(f"Error getting financial metrics for {symbol}: {e}")
            return None
    
    def _calculate_revenue_growth(self, annual_results) -> Optional[float]:
        """Calculate revenue growth from annual results"""
        if len(annual_results) < 2:
            return None
        
        try:
            # Sort by period to get chronological order
            sorted_results = sorted(annual_results, key=lambda x: x.period)
            latest = sorted_results[-1]
            previous = sorted_results[-2]
            
            if latest.sales_in_cr and previous.sales_in_cr and previous.sales_in_cr > 0:
                return (latest.sales_in_cr - previous.sales_in_cr) / previous.sales_in_cr
        except:
            pass
        return None
    
    def _calculate_earnings_growth(self, annual_results) -> Optional[float]:
        """Calculate earnings growth from annual results"""
        if len(annual_results) < 2:
            return None
        
        try:
            sorted_results = sorted(annual_results, key=lambda x: x.period)
            latest = sorted_results[-1]
            previous = sorted_results[-2]
            
            if latest.net_profit_in_cr and previous.net_profit_in_cr and previous.net_profit_in_cr > 0:
                return (latest.net_profit_in_cr - previous.net_profit_in_cr) / previous.net_profit_in_cr
        except:
            pass
        return None
    
    def _calculate_net_margin(self, annual_results) -> Optional[float]:
        """Calculate net margin from latest annual results"""
        if not annual_results:
            return None
        
        try:
            latest = sorted(annual_results, key=lambda x: x.period)[-1]
            if latest.net_profit_in_cr and latest.sales_in_cr and latest.sales_in_cr > 0:
                return latest.net_profit_in_cr / latest.sales_in_cr
        except:
            pass
        return None
    
    def _calculate_operating_margin(self, annual_results) -> Optional[float]:
        """Calculate operating margin from latest annual results"""
        if not annual_results:
            return None
        
        try:
            latest = sorted(annual_results, key=lambda x: x.period)[-1]
            if latest.operating_profit_in_cr and latest.sales_in_cr and latest.sales_in_cr > 0:
                return latest.operating_profit_in_cr / latest.sales_in_cr
        except:
            pass
        return None
    
    def _calculate_current_ratio(self, balance_sheets) -> Optional[float]:
        """Calculate current ratio from balance sheet data"""
        # This would need more detailed current assets/liabilities data
        # For now, return None as ScreenerScraper doesn't provide detailed breakdown
        return None
    
    def _calculate_debt_to_equity(self, balance_sheets) -> Optional[float]:
        """Calculate debt to equity ratio"""
        if not balance_sheets:
            return None
        
        try:
            latest = sorted(balance_sheets, key=lambda x: x.period)[-1]
            if latest.borrowings_in_cr and latest.equity_capital_in_cr and latest.equity_capital_in_cr > 0:
                return latest.borrowings_in_cr / latest.equity_capital_in_cr
        except:
            pass
        return None
    
    def _get_latest_eps(self, annual_results) -> Optional[float]:
        """Get latest EPS from annual results"""
        if not annual_results:
            return None
        
        try:
            latest = sorted(annual_results, key=lambda x: x.period)[-1]
            return latest.eps if hasattr(latest, 'eps') else None
        except:
            pass
        return None
    
    def _calculate_pb_ratio(self, key_metrics) -> Optional[float]:
        """Calculate P/B ratio from current price and book value"""
        if key_metrics.current_price and key_metrics.book_value and key_metrics.book_value > 0:
            return key_metrics.current_price / key_metrics.book_value
        return None
