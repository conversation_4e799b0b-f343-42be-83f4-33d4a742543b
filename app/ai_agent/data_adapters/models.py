"""
Data models for AI analysis agents
"""
from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime


class PriceData(BaseModel):
    """Price data model for technical analysis"""
    datetime: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    delivery_volume: int = 0


class FinancialMetrics(BaseModel):
    """Financial metrics for fundamental analysis"""
    # Profitability metrics
    return_on_equity: Optional[float] = None
    return_on_assets: Optional[float] = None
    net_margin: Optional[float] = None
    operating_margin: Optional[float] = None
    gross_margin: Optional[float] = None
    
    # Growth metrics
    revenue_growth: Optional[float] = None
    earnings_growth: Optional[float] = None
    book_value_growth: Optional[float] = None
    
    # Financial health metrics
    current_ratio: Optional[float] = None
    debt_to_equity: Optional[float] = None
    free_cash_flow_per_share: Optional[float] = None
    earnings_per_share: Optional[float] = None
    
    # Valuation metrics
    price_to_earnings_ratio: Optional[float] = None
    price_to_book_ratio: Optional[float] = None
    price_to_sales_ratio: Optional[float] = None
    market_cap: Optional[float] = None
    
    # Additional metrics
    dividend_yield: Optional[float] = None
    peg_ratio: Optional[float] = None


class CompanyData(BaseModel):
    """Company information for analysis"""
    symbol: str
    company_name: str
    sector: str
    about: str
    current_price: Optional[float] = None
    market_cap_in_cr: Optional[float] = None


class AnalysisSignal(BaseModel):
    """Analysis signal from an agent"""
    signal: str  # "bullish", "bearish", "neutral"
    confidence: float  # 0.0 to 1.0
    reasoning: dict
    timestamp: datetime = Field(default_factory=datetime.now)


class StockAnalysisData(BaseModel):
    """Complete stock analysis data package"""
    symbol: str
    company_data: CompanyData
    financial_metrics: FinancialMetrics
    price_data: List[PriceData]
    analysis_date: datetime = Field(default_factory=datetime.now)
