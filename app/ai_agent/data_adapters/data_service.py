"""
Main data service that combines ScreenerScraper and StrikeAPI data
"""
from typing import Optional
from app.ai_agent.data_adapters.screener_adapter import ScreenerDataAdapter
from app.ai_agent.data_adapters.strike_adapter import StrikeDataAdapter
from app.ai_agent.data_adapters.models import StockAnalysisData


class DataService:
    """Main service to get all stock data for analysis"""
    
    def __init__(self):
        self.screener_adapter = ScreenerDataAdapter()
        self.strike_adapter = StrikeDataAdapter()
    
    def get_stock_analysis_data(self, symbol: str, days_back: int = 365) -> Optional[StockAnalysisData]:
        """Get complete stock data for analysis"""
        try:
            # Get company data from ScreenerScraper
            company_data = self.screener_adapter.get_company_data(symbol)
            if not company_data:
                print(f"Could not get company data for {symbol}")
                return None
            
            # Get financial metrics from ScreenerScraper
            financial_metrics = self.screener_adapter.get_financial_metrics(symbol)
            if not financial_metrics:
                print(f"Could not get financial metrics for {symbol}")
                return None
            
            # Get price data from StrikeAPI
            price_data = self.strike_adapter.get_price_data(symbol, days_back)
            if not price_data:
                print(f"Could not get price data for {symbol}")
                return None
            
            return StockAnalysisData(
                symbol=symbol,
                company_data=company_data,
                financial_metrics=financial_metrics,
                price_data=price_data
            )
        except Exception as e:
            print(f"Error getting stock analysis data for {symbol}: {e}")
            return None
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """Get latest price for a symbol"""
        return self.strike_adapter.get_latest_price(symbol)
    
    def get_price_dataframe(self, symbol: str, days_back: int = 365):
        """Get price data as DataFrame for technical analysis"""
        return self.strike_adapter.get_price_dataframe(symbol, days_back)
