"""
Example usage scenarios for the AI Stock Analysis Agent
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.ai_agent.workflow.graph import run_stock_analysis, run_batch_analysis
from app.ai_agent.utils.display import print_analysis_summary, print_batch_summary, export_results_to_json


def example_1_single_stock_analysis():
    """Example 1: Analyze a single stock with detailed output"""
    print("=" * 60)
    print("EXAMPLE 1: Single Stock Analysis")
    print("=" * 60)
    
    # Analyze TATAMOTORS with medium risk tolerance and medium-term timeframe
    symbol = "TATAMOTORS"
    result = run_stock_analysis(symbol, "medium", "medium_term")
    
    # Display the complete analysis
    print_analysis_summary(result)
    
    # Access specific results programmatically
    if result.final_recommendation:
        recommendation = result.final_recommendation["recommendation"]
        print(f"\nProgrammatic Access:")
        print(f"Action: {recommendation['action']}")
        print(f"Confidence: {recommendation['confidence']:.2f}")
        
        if "price_targets" in recommendation.get("reasoning", {}):
            targets = recommendation["reasoning"]["price_targets"]
            print(f"Target Price: ₹{targets.get('target_price', 'N/A')}")
            print(f"Stop Loss: ₹{targets.get('stop_loss', 'N/A')}")


def example_2_batch_analysis():
    """Example 2: Batch analysis of multiple stocks"""
    print("\n" + "=" * 60)
    print("EXAMPLE 2: Batch Analysis")
    print("=" * 60)
    
    # Analyze multiple stocks
    symbols = ["TATAMOTORS", "RELIANCE", "INFY"]
    results = run_batch_analysis(symbols, "medium", "medium_term")
    
    # Display batch summary
    print_batch_summary(results)
    
    # Process results programmatically
    print(f"\nProgrammatic Processing:")
    buy_recommendations = []
    sell_recommendations = []
    hold_recommendations = []
    
    for symbol, result in results.items():
        if result.final_recommendation:
            action = result.final_recommendation["recommendation"]["action"]
            confidence = result.final_recommendation["recommendation"]["confidence"]
            
            if action in ["buy", "strong_buy"]:
                buy_recommendations.append((symbol, confidence))
            elif action in ["sell", "strong_sell"]:
                sell_recommendations.append((symbol, confidence))
            else:
                hold_recommendations.append((symbol, confidence))
    
    print(f"Buy recommendations: {len(buy_recommendations)}")
    print(f"Sell recommendations: {len(sell_recommendations)}")
    print(f"Hold recommendations: {len(hold_recommendations)}")


def example_3_risk_tolerance_comparison():
    """Example 3: Compare analysis with different risk tolerances"""
    print("\n" + "=" * 60)
    print("EXAMPLE 3: Risk Tolerance Comparison")
    print("=" * 60)
    
    symbol = "RELIANCE"
    risk_levels = ["low", "medium", "high"]
    
    print(f"Analyzing {symbol} with different risk tolerances:")
    
    for risk_level in risk_levels:
        print(f"\n--- Risk Tolerance: {risk_level.upper()} ---")
        result = run_stock_analysis(symbol, risk_level, "medium_term")
        
        if result.final_recommendation:
            recommendation = result.final_recommendation["recommendation"]
            risk_assessment = result.final_recommendation.get("risk_level", "unknown")
            
            print(f"Recommendation: {recommendation['action'].upper()}")
            print(f"Confidence: {recommendation['confidence']:.2f}")
            print(f"Risk Level: {risk_assessment.upper()}")
        else:
            print("Analysis failed")


def example_4_timeframe_comparison():
    """Example 4: Compare analysis with different timeframes"""
    print("\n" + "=" * 60)
    print("EXAMPLE 4: Investment Timeframe Comparison")
    print("=" * 60)
    
    symbol = "INFY"
    timeframes = ["short_term", "medium_term", "long_term"]
    
    print(f"Analyzing {symbol} with different investment timeframes:")
    
    for timeframe in timeframes:
        print(f"\n--- Timeframe: {timeframe.replace('_', ' ').title()} ---")
        result = run_stock_analysis(symbol, "medium", timeframe)
        
        if result.final_recommendation:
            recommendation = result.final_recommendation["recommendation"]
            
            print(f"Recommendation: {recommendation['action'].upper()}")
            print(f"Confidence: {recommendation['confidence']:.2f}")
            
            # Show reasoning summary
            reasoning = recommendation.get("reasoning", {})
            if "decision_rationale" in reasoning:
                rationale = reasoning["decision_rationale"][:100] + "..." if len(reasoning["decision_rationale"]) > 100 else reasoning["decision_rationale"]
                print(f"Rationale: {rationale}")
        else:
            print("Analysis failed")


def example_5_export_and_processing():
    """Example 5: Export results and process them"""
    print("\n" + "=" * 60)
    print("EXAMPLE 5: Export and Data Processing")
    print("=" * 60)
    
    # Analyze a few stocks
    symbols = ["TATAMOTORS", "RELIANCE"]
    results = run_batch_analysis(symbols, "medium", "medium_term")
    
    # Export to JSON
    export_results_to_json(results, "example_analysis_results.json")
    
    # Process the results for a custom report
    print(f"\nCustom Report Generation:")
    print("-" * 30)
    
    total_analyzed = len(results)
    successful_analyses = sum(1 for r in results.values() if r.final_recommendation)
    
    print(f"Total stocks analyzed: {total_analyzed}")
    print(f"Successful analyses: {successful_analyses}")
    
    # Create a simple portfolio recommendation
    portfolio_allocation = {}
    total_confidence = 0
    
    for symbol, result in results.items():
        if result.final_recommendation:
            action = result.final_recommendation["recommendation"]["action"]
            confidence = result.final_recommendation["recommendation"]["confidence"]
            
            if action in ["buy", "strong_buy"]:
                # Allocate based on confidence (simplified approach)
                allocation = confidence * 20  # Max 20% per stock
                portfolio_allocation[symbol] = allocation
                total_confidence += confidence
    
    # Normalize allocations
    if total_confidence > 0:
        print(f"\nSuggested Portfolio Allocation:")
        for symbol, allocation in portfolio_allocation.items():
            normalized_allocation = (allocation / sum(portfolio_allocation.values())) * 100
            print(f"{symbol}: {normalized_allocation:.1f}%")


def example_6_error_handling():
    """Example 6: Demonstrate error handling"""
    print("\n" + "=" * 60)
    print("EXAMPLE 6: Error Handling")
    print("=" * 60)
    
    # Try to analyze an invalid symbol
    invalid_symbol = "INVALID_SYMBOL"
    print(f"Attempting to analyze invalid symbol: {invalid_symbol}")
    
    result = run_stock_analysis(invalid_symbol, "medium", "medium_term")
    
    if result.errors:
        print(f"Errors encountered: {len(result.errors)}")
        for error in result.errors:
            print(f"  - {error}")
    
    if not result.final_recommendation:
        print("Analysis failed as expected for invalid symbol")
    
    # Show how the system gracefully handles errors
    print(f"Completed steps: {result.completed_steps}")
    print(f"Current step: {result.current_step}")


def run_all_examples():
    """Run all example scenarios"""
    print("🚀 AI Stock Analysis Agent - Example Usage Scenarios")
    print("=" * 60)
    
    examples = [
        example_1_single_stock_analysis,
        example_2_batch_analysis,
        example_3_risk_tolerance_comparison,
        example_4_timeframe_comparison,
        example_5_export_and_processing,
        example_6_error_handling
    ]
    
    for i, example_func in enumerate(examples, 1):
        try:
            example_func()
        except Exception as e:
            print(f"\n❌ Example {i} failed: {e}")
        
        if i < len(examples):
            input(f"\nPress Enter to continue to Example {i+1}...")
    
    print(f"\n🎉 All examples completed!")
    print(f"\nNext steps:")
    print(f"1. Try the CLI: python -m app.ai_agent.cli TATAMOTORS")
    print(f"2. Use interactive mode: python -m app.ai_agent.cli --interactive")
    print(f"3. Customize the analysis for your needs")


if __name__ == "__main__":
    run_all_examples()
